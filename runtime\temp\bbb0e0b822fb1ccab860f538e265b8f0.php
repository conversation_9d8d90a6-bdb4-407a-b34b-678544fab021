<?php /*a:1:{s:56:"/var/www/html/application/manage/view/bet/task_list.html";i:1754248805;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>项目列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form search">
                        <div class="layui-form-item">
                        <div class="layui-inline">
                                <label class="layui-form-label">发布人</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="username" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">标题</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="title" autocomplete="off">
                                </div>
                            </div>
                            <!--<div class="layui-inline">
                                <label class="layui-form-label">任务类型</label>
                                <div class="layui-input-inline">
                                    <select name="task_type" lay-verify="required">
                                        <option value="">全部</option>
                                        <option value="1">供应信息</option>
                                        <option value="2">需求信息</option>
                                    </select>
                                </div>
                            </div>-->
                            <div class="layui-inline">
                                <label class="layui-form-label">任务分类</label>
                                <div class="layui-input-inline">
                                    <select name="task_class" lay-verify="required">
                                        <option value="">所有</option>
                                        <?php foreach($taskClass as $key=>$value): ?>
                                        <option value="<?php echo htmlentities($value['id']); ?>"><?php echo htmlentities($value['group_name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">发布时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="datetime_range" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-inline">
                                    <select name="status" lay-verify="required" lay-search="">
                                        <option value="">全部</option>
                                        <?php foreach(app('config')->get('custom.taskStatus') as $key=>$value): ?>
                                        <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($value); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">显示状态</label>
                                <div class="layui-input-inline">
                                    <select name="is_visible" lay-search="">
                                        <option value="">全部</option>
                                        <option value="1">显示</option>
                                        <option value="0">隐藏</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline" style="text-align: center;">
                                <button type="button" class="layui-btn" data-type="search">搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="taskList" lay-filter="taskList"></table>
                </div>
            </div>
        </div>
    </div>
    <!-- 头部左侧工具栏 -->
    <script type="text/html" id="toolbarDemo">
        <div class="layui-btn-container layui-btn-group">
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
                <i class="layui-icon">&#xe654;</i>新增
            </button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-event="del">
                <i class="layui-icon">&#xe640;</i>删除
            </button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-event="task-class">任务分类</button>
			<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-event="task-tpl-add">从模板添加</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-event="batch-import">
                <i class="layui-icon">&#xe67c;</i>批量导入
            </button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-event="batch-rebate">
                <i class="layui-icon">&#xe642;</i>批量修改分佣
            </button>
        </div>
    </script>
    <!-- 表格右侧操作单元 -->
    <script type="text/html" id="action">
        <div class="layui-btn-group">
            {{# if (d.status == 1) { }}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="audit">审核</button>
            {{# } }}
            {{# if (d.revoke == 1) { }}
             <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="audit">撤销</button>
            {{# } }}
            <button type="button" class="layui-btn layui-btn-xs" lay-event="edit">
                <i class="layui-icon">&#xe642;</i>
            </button>
            {{# if (d.is_visible == 1) { }}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm" lay-event="hide">隐藏</button>
            {{# } else { }}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="show">显示</button>
            {{# } }}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">
                <i class="layui-icon">&#xe640;</i>
            </button>
        </div>
    </script>

    <!-- 批量修改分佣弹窗 -->
    <script type="text/html" id="batchRebateForm">
        <form class="layui-form" lay-filter="batchRebateForm" style="padding: 20px;">
            <div class="layui-form-item">
                <label class="layui-form-label">一级分佣(%)</label>
                <div class="layui-input-block">
                    <input type="number" name="task_rebate1" placeholder="请输入一级分佣比例" autocomplete="off" class="layui-input" step="0.0001" min="0" max="9999.9999">
                    <div class="layui-form-mid layui-word-aux">支持范围：0% - 9999.9999%</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">二级分佣(%)</label>
                <div class="layui-input-block">
                    <input type="number" name="task_rebate2" placeholder="请输入二级分佣比例" autocomplete="off" class="layui-input" step="0.0001" min="0" max="9999.9999">
                    <div class="layui-form-mid layui-word-aux">支持范围：0% - 9999.9999%</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">三级分佣(%)</label>
                <div class="layui-input-block">
                    <input type="number" name="task_rebate3" placeholder="请输入三级分佣比例" autocomplete="off" class="layui-input" step="0.0001" min="0" max="9999.9999">
                    <div class="layui-form-mid layui-word-aux">支持范围：0% - 9999.9999%</div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit lay-filter="batchRebateSubmit">确认修改</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </script>

    <!-- 批量导入弹窗 -->
    <script type="text/html" id="batchImportForm">
        <div style="padding: 20px;">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">下载模板</label>
                    <div class="layui-input-inline">
                        <button type="button" class="layui-btn layui-btn-normal" id="downloadTemplate">
                            <i class="layui-icon">&#xe601;</i>下载Excel模板
                        </button>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">选择文件</label>
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" id="selectFile">
                        <i class="layui-icon">&#xe67c;</i>选择Excel文件
                    </button>
                    <div class="layui-upload-list" style="margin-top: 10px;">
                        <div id="fileList"></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-btn-normal" id="startImport" disabled>
                        <i class="layui-icon">&#xe67c;</i>开始导入
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" id="cancelImport">取消</button>
                </div>
            </div>
            <div class="layui-form-item" id="importProgress" style="display: none;">
                <label class="layui-form-label">导入进度</label>
                <div class="layui-input-block">
                    <div class="layui-progress" lay-showpercent="true">
                        <div class="layui-progress-bar" lay-percent="0%"></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" id="importResult" style="display: none;">
                <label class="layui-form-label">导入结果</label>
                <div class="layui-input-block">
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
    </script>

    <!-- 表单元素 -->

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bet.js"></script>
<script>
    layui.use(['layer', 'table', 'form', 'upload', 'element'], function(){
        var $ = layui.$
        ,layer = layui.layer
        ,table = layui.table
        ,form = layui.form
        ,upload = layui.upload
        ,element = layui.element;

        //方法级渲染
        table.render({
            elem: '#taskList'
            ,title: '任务列表'
            ,url: '/manage/bet/taskList'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true, totalRowText: '合计'}
                ,{field: 'id', title: '编号', sort: true, fixed: 'left'}
				,{field: 'username', title: '发布人', sort: true, fixed: 'left'}

                ,{field: 'title', title: '标题', sort: true, fixed: 'left'}
                ,{field: 'main_image', title: '主图', width: 100, templet: function(d){
                    if(d.main_image && d.main_image !== '') {
                        return '<img src="' + d.main_image + '" style="max-width: 60px; max-height: 60px; cursor: pointer;" onclick="layer.photos({photos: {data: [{src: \'' + d.main_image + '\'}]}, anim: 5});">';
                    } else {
                        return '<span style="color: #999;">无图片</span>';
                    }
                }}
                ,{field: 'detail_image', title: '详情图', width: 100, templet: function(d){
                    if(d.detail_image && d.detail_image !== '') {
                        try {
                            var images = JSON.parse(d.detail_image);
                            if(Array.isArray(images) && images.length > 0) {
                                var firstImage = images[0];
                                var count = images.length;
                                // 使用data属性存储图片数据，避免字符串转义问题
                                var encodedImages = encodeURIComponent(d.detail_image);
                                return '<img src="' + firstImage + '" style="max-width: 60px; max-height: 60px; cursor: pointer;" class="detail-image-preview" data-images="' + encodedImages + '">' +
                                       '<br><span style="font-size: 12px; color: #666;">共' + count + '张</span>';
                            }
                        } catch(e) {
                            // 兼容旧数据（单个URL字符串）
                            if(d.detail_image.startsWith('http')) {
                                return '<img src="' + d.detail_image + '" style="max-width: 60px; max-height: 60px; cursor: pointer;" class="single-image-preview" data-src="' + d.detail_image + '">';
                            }
                        }
                    }
                    return '<span style="color: #999;">无图片</span>';
                }}
                ,{field: 'task_type', title: '任务类型', sort: true, templet: function(d){
                    return d.task_type_str;
                }}
                // ,{field: '', title: '任务级别', sort: true}
                ,{field: 'receive_number', title: '已领/名额', templet: function(d){
                    return d.speed;
                }}
                ,{field: 'task_class', title: '任务分类', sort: true, templet: function(d){
                    return d.group_name;
                }}
                ,{field: 'purchase_price', title: '购买价格', sort: true, totalRow: true}
                ,{field: 'task_commission', title: '任务佣金', sort: true, totalRow: true}
                ,{field: 'total_price', title: '总价+抽水', templet: function(d){
                    return d.speed_total_price;
                }}
                ,{field: 'task_rebate1', title: '一级分佣%', sort: true}
                ,{field: 'task_rebate2', title: '二级分佣%', sort: true}
                ,{field: 'task_rebate3', title: '三级分佣%', sort: true}
                ,{field: 'end_time', title: '截止日期', sort: true, templet: function(d){
                    return d.format_end_time;
                }}
                ,{field: 'status', title: '当前状态', sort: true, templet: function(d){
                    return d.statusStr;
                }}
                ,{field: 'is_visible', title: '显示状态', sort: true, templet: function(d){
                    if(d.is_visible == 1) {
                        return '<span style="color: green;">显示</span>';
                    } else {
                        return '<span style="color: red;">隐藏</span>';
                    }
                }}
                ,{field: 'add_time', title: '添加时间', sort: true, templet: function(d){
                    return d.format_add_time;
                }}
                ,{title: '管理操作', width: '20%', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
            ,done: function(res, curr, count){
                // 表格渲染完成后绑定工具栏事件
                bindToolbarEvents();
            }
        });

        //监听排序事件
        table.on('sort(taskList)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload('taskList', {
                initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sortField: obj.field //排序字段
                    ,sortType: obj.type //排序方式
                }
            });
        });
        //监听行双击事件
        table.on('rowDouble(taskList)', function(obj){

        });

        // 绑定工具栏事件的函数
        function bindToolbarEvents() {
            // 头部左侧工具栏事件
            table.on('toolbar(taskList)', function(obj){
			switch(obj.event){
				case 'add':
					layer.open({
						type: 2,
						title: "新增任务",
						area: ['95%','95%'],
						content: "/manage/bet/taskAdd"
					});
					break;

				case 'task-tpl-add':
					layer.open({
						type: 2,
						title: "从模板添加任务",
						area: ['95%','95%'],
						content: "/manage/bet/taskTplAdd"
					});
					break;

				case 'del':
					var checkStatus = table.checkStatus('taskList');
					var data = checkStatus.data;
					if(data.length === 0){
						layer.msg('请先选择要删除的任务', {icon: 2});
						return;
					}

					var ids = [];
					var titles = [];
					for(var i = 0; i < data.length; i++){
						ids.push(data[i].id);
						titles.push(data[i].title);
					}

					var confirmText = '确定要删除以下 ' + data.length + ' 个任务吗？\n\n' + titles.join('\n');
					layer.confirm(confirmText, {
						btn: ['确定','取消'],
						area: ['400px', '300px']
					}, function(index){
						$.ajax({
							url: "/manage/bet/taskDel",
							data: {
								ids: ids.join(',')
							},
							type: "POST",
							dataType: "json",
							timeout: 30000,
							beforeSend: function(){
								layer.load(1);
							},
							success: function(msg){
								var alertStr = (msg == 1) ? '批量删除成功' : msg;
								layer.msg(alertStr, {time: 2000}, function(){
									if(msg == 1){
										layer.closeAll();
										table.reload('taskList');
									}
								});
							},
							complete: function(){
								layer.closeAll("loading");
							},
							error: function(){
								layer.closeAll("loading");
								layer.msg('批量删除失败，请重试', {icon: 2});
							}
						});
						layer.close(index);
					}, function(){
						layer.msg('取消删除', {time: 1000});
					});
					break;

				case 'task-class':
                    layer.open({
                        type: 2,
                        title: "项目类型",
                        area: ['90%','90%'],
                        content: "/manage/bet/TaskClass"
                    });
                    break;

                case 'batch-import':
                    // 打开批量导入弹窗
                    layer.open({
                        type: 1,
                        title: '批量导入任务',
                        area: ['600px', '500px'],
                        content: $('#batchImportForm').html(),
                        success: function(layero, index){
                            var selectedFile = null;

                            // 下载模板按钮事件
                            $(layero).find('#downloadTemplate').on('click', function(){
                                var $btn = $(this);
                                $btn.prop('disabled', true).text('生成中...');

                                // 直接跳转到下载链接
                                window.location.href = '/manage/bet/downloadTaskTemplate';

                                // 恢复按钮状态
                                setTimeout(function(){
                                    $btn.prop('disabled', false).html('<i class="layui-icon">&#xe601;</i>下载Excel模板');
                                }, 2000);
                            });

                            // 文件选择
                            upload.render({
                                elem: $(layero).find('#selectFile')[0],
                                url: '', // 不自动上传
                                accept: 'file',
                                exts: 'xlsx|xls',
                                auto: false,
                                choose: function(obj){
                                    var files = obj.pushFile();
                                    obj.preview(function(index, file, result){
                                        selectedFile = file;
                                        $(layero).find('#fileList').html('<p>已选择文件：' + file.name + '</p>');
                                        $(layero).find('#startImport').prop('disabled', false);
                                    });
                                }
                            });

                            // 开始导入按钮事件
                            $(layero).find('#startImport').on('click', function(){
                                if(!selectedFile){
                                    layer.msg('请先选择Excel文件', {icon: 2});
                                    return;
                                }

                                // 显示进度条
                                $(layero).find('#importProgress').show();
                                $(layero).find('#startImport').prop('disabled', true);

                                // 创建FormData对象
                                var formData = new FormData();
                                formData.append('excel_file', selectedFile);

                                // 上传并导入
                                $.ajax({
                                    url: '/manage/bet/batchImportTask',
                                    type: 'POST',
                                    data: formData,
                                    processData: false,
                                    contentType: false,
                                    xhr: function() {
                                        var xhr = $.ajaxSettings.xhr();
                                        if (xhr.upload) {
                                            xhr.upload.addEventListener('progress', function(e) {
                                                if (e.lengthComputable) {
                                                    var percent = Math.round((e.loaded / e.total) * 100);
                                                    $(layero).find('.layui-progress-bar').attr('lay-percent', percent + '%').css('width', percent + '%');
                                                    $(layero).find('.layui-progress-text').text(percent + '%');
                                                }
                                            }, false);
                                        }
                                        return xhr;
                                    },
                                    success: function(res){
                                        console.log('导入响应:', res); // 调试信息
                                        $(layero).find('#importProgress').hide();
                                        $(layero).find('#importResult').show();

                                        if(res.code == 1){
                                            var resultHtml = '<div class="layui-text">';
                                            resultHtml += '<p style="color: green;">导入成功！</p>';
                                            resultHtml += '<p>成功导入：' + res.data.success_count + ' 条</p>';
                                            if(res.data.fail_count > 0){
                                                resultHtml += '<p style="color: red;">失败：' + res.data.fail_count + ' 条</p>';
                                                if(res.data.errors && res.data.errors.length > 0){
                                                    resultHtml += '<p>错误详情：</p><ul>';
                                                    for(var i = 0; i < res.data.errors.length; i++){
                                                        resultHtml += '<li style="color: red;">' + res.data.errors[i] + '</li>';
                                                    }
                                                    resultHtml += '</ul>';
                                                }
                                            }
                                            resultHtml += '</div>';
                                            $(layero).find('#resultContent').html(resultHtml);

                                            // 刷新表格
                                            table.reload('taskList');
                                        } else {
                                            var errorHtml = '<div class="layui-text">';
                                            errorHtml += '<p style="color: red;">导入失败：' + (res.msg || '未知错误') + '</p>';
                                            if(res.errors && res.errors.length > 0){
                                                errorHtml += '<p>错误详情：</p><ul>';
                                                for(var i = 0; i < res.errors.length; i++){
                                                    errorHtml += '<li style="color: red;">' + res.errors[i] + '</li>';
                                                }
                                                errorHtml += '</ul>';
                                            }
                                            errorHtml += '</div>';
                                            $(layero).find('#resultContent').html(errorHtml);
                                        }

                                        $(layero).find('#startImport').prop('disabled', false);
                                    },
                                    error: function(xhr, status, error){
                                        console.log('AJAX错误:', xhr, status, error); // 调试信息
                                        $(layero).find('#importProgress').hide();
                                        $(layero).find('#importResult').show();
                                        $(layero).find('#startImport').prop('disabled', false);

                                        var errorHtml = '<div class="layui-text">';
                                        errorHtml += '<p style="color: red;">网络错误：' + error + '</p>';
                                        errorHtml += '<p>状态码：' + xhr.status + '</p>';
                                        if(xhr.responseText){
                                            errorHtml += '<p>响应内容：' + xhr.responseText.substring(0, 500) + '</p>';
                                        }
                                        errorHtml += '</div>';
                                        $(layero).find('#resultContent').html(errorHtml);
                                    }
                                });
                            });

                            // 取消按钮事件
                            $(layero).find('#cancelImport').on('click', function(){
                                layer.close(index);
                            });
                        }
                    });
                    break;

                case 'batch-rebate':
                    // 获取选中的行
                    var checkStatus = table.checkStatus('taskList');
                    var data = checkStatus.data;

                    if(data.length === 0){
                        layer.msg('请先选择要修改的任务', {icon: 2});
                        return;
                    }

                    // 打开批量修改分佣弹窗
                    layer.open({
                        type: 1,
                        title: '批量修改分佣比例',
                        area: ['500px', '400px'],
                        content: $('#batchRebateForm').html(),
                        success: function(layero, index){
                            // 重新渲染表单
                            form.render();

                            // 监听表单提交
                            form.on('submit(batchRebateSubmit)', function(formData){
                                var taskIds = [];
                                for(var i = 0; i < data.length; i++){
                                    taskIds.push(data[i].id);
                                }

                                var postData = {
                                    task_ids: taskIds.join(','),
                                    task_rebate1: formData.field.task_rebate1,
                                    task_rebate2: formData.field.task_rebate2,
                                    task_rebate3: formData.field.task_rebate3
                                };

                                $.post('/manage/bet/batchUpdateRebate', postData, function(res){
                                    if(res.code == 0){
                                        layer.msg('批量修改成功', {icon: 1});
                                        layer.close(index);
                                        table.reload('taskList');
                                    } else {
                                        layer.msg(res.msg || '修改失败', {icon: 2});
                                    }
                                }, 'json');

                                return false;
                            });
                        }
                    });
                    break;
			};
		});
        }

        active = {
            search: function(){
                //执行重载
                table.reload('taskList', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
						username: $("input[name='username']").val()
                        ,title: $("input[name='title']").val()
                        ,datetime_range: $("input[name='datetime_range']").val()
                        ,task_type: $("select[name='task_type'] option:selected").val()
                        ,task_class: $("select[name='task_class'] option:selected").val()
						,status: $("select[name='status'] option:selected").val()
						,is_visible: $("select[name='is_visible'] option:selected").val()
                    }
                }, 'data');
            }
        };

        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        // 行工具栏事件处理
        table.on('tool(taskList)', function(obj){
            var data = obj.data;
            var layEvent = obj.event;
            var tr = obj.tr;

            switch(layEvent){
                case 'edit':
                    layer.open({
                        type: 2,
                        title: "编辑任务",
                        area: ['95%','95%'],
                        content: "/manage/bet/taskEdit?id=" + data.id
                    });
                    break;

                case 'hide':
                    layer.confirm('确定要隐藏此任务吗？', function(index){
                        $.post('/manage/bet/taskToggleVisible', {
                            id: data.id,
                            is_visible: 0
                        }, function(res){
                            if(res.code == 0){
                                layer.msg('操作成功', {icon: 1});
                                table.reload('taskList');
                            } else {
                                layer.msg(res.msg || '操作失败', {icon: 2});
                            }
                        });
                        layer.close(index);
                    });
                    break;

                case 'show':
                    layer.confirm('确定要显示此任务吗？', function(index){
                        $.post('/manage/bet/taskToggleVisible', {
                            id: data.id,
                            is_visible: 1
                        }, function(res){
                            if(res.code == 0){
                                layer.msg('操作成功', {icon: 1});
                                table.reload('taskList');
                            } else {
                                layer.msg(res.msg || '操作失败', {icon: 2});
                            }
                        });
                        layer.close(index);
                    });
                    break;

                case 'del':
                    layer.confirm('确定要删除任务 "' + data.title + '" 吗？', {
                        btn: ['确定','取消']
                    }, function(index){
                        $.ajax({
                            url: "/manage/bet/taskDel",
                            data: {
                                id: data.id
                            },
                            type: "POST",
                            dataType: "json",
                            timeout: 15000,
                            beforeSend: function(){
                                layer.load(1);
                            },
                            success: function(msg){
                                var alertStr = (msg == 1) ? '删除成功' : msg;
                                layer.msg(alertStr, {time: 2000}, function(){
                                    if(msg == 1){
                                        layer.closeAll();
                                        table.reload('taskList');
                                    }
                                });
                            },
                            complete: function(){
                                layer.closeAll("loading");
                            },
                            error: function(){
                                layer.closeAll("loading");
                                layer.msg('删除失败，请重试', {icon: 2});
                            }
                        });
                        layer.close(index);
                    }, function(){
                        layer.msg('取消删除', {time: 1000});
                    });
                    break;
            }
        });

        // 使用事件委托处理详情图点击
        $(document).on('click', '.detail-image-preview', function() {
            var encodedImages = $(this).data('images');
            if(encodedImages) {
                try {
                    var detailImageJson = decodeURIComponent(encodedImages);
                    var images = JSON.parse(detailImageJson);
                    if(Array.isArray(images) && images.length > 0) {
                        var photoData = images.map(function(url) {
                            return {src: url};
                        });
                        layer.photos({
                            photos: {data: photoData},
                            anim: 5
                        });
                    }
                } catch(e) {
                    layer.msg('图片数据格式错误', {icon: 2});
                }
            }
        });

        // 兼容单张图片预览
        $(document).on('click', '.single-image-preview', function() {
            var src = $(this).data('src');
            if(src) {
                layer.photos({
                    photos: {data: [{src: src}]},
                    anim: 5
                });
            }
        });

        // 备用事件绑定，确保工具栏事件能正常工作
        setTimeout(function(){
            if (typeof bindToolbarEvents === 'function') {
                bindToolbarEvents();
            }
        }, 1000);
    });
</script>
</body>
</html>