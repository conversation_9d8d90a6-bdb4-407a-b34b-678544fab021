---------------------------------------------------------------

[2025-08-04T03:33:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001497s ] mysql:host=mysql;dbname=di<PERSON><PERSON><PERSON>;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001790s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249626 LIMIT 100 [ RunTime:0.000773s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000897s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000342s ]
---------------------------------------------------------------

[2025-08-04T03:34:12+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001111s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001386s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000544s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000735s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000977s ]
[ error ] [8]未定义变量: userLevel
---------------------------------------------------------------

[2025-08-04T03:34:14+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001066s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000864s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.002496s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000277s ]
[ error ] [8]未定义变量: userLevel
---------------------------------------------------------------

[2025-08-04T03:34:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000875s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000530s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249687 LIMIT 100 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001091s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000225s ]
---------------------------------------------------------------

[2025-08-04T03:34:57+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001174s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000471s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000543s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000291s ]
---------------------------------------------------------------

[2025-08-04T03:34:57+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001017s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000494s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000839s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000443s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 0,10 [ RunTime:0.003376s ]
---------------------------------------------------------------

[2025-08-04T03:34:57+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001214s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001190s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000461s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000956s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000407s ]
---------------------------------------------------------------

[2025-08-04T03:35:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001586s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001237s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249747 LIMIT 100 [ RunTime:0.000615s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001054s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000267s ]
---------------------------------------------------------------

[2025-08-04T03:35:48+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001183s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000886s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000584s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.001053s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000559s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 10,10 [ RunTime:0.003821s ]
---------------------------------------------------------------

[2025-08-04T03:35:51+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000884s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000579s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000730s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000391s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 20,10 [ RunTime:0.002934s ]
---------------------------------------------------------------

[2025-08-04T03:35:52+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000796s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000502s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000369s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 30,10 [ RunTime:0.003061s ]
---------------------------------------------------------------

[2025-08-04T03:35:54+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001090s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000689s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000354s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 40,10 [ RunTime:0.002786s ]
---------------------------------------------------------------

[2025-08-04T03:35:55+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000988s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000453s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000526s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 50,10 [ RunTime:0.002902s ]
---------------------------------------------------------------

[2025-08-04T03:35:57+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000885s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000482s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000255s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 60,10 [ RunTime:0.003079s ]
---------------------------------------------------------------

[2025-08-04T03:35:59+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000886s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.001037s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000673s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000367s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 70,10 [ RunTime:0.002860s ]
---------------------------------------------------------------

[2025-08-04T03:36:00+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000988s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000648s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000466s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000364s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 80,10 [ RunTime:0.003012s ]
---------------------------------------------------------------

[2025-08-04T03:36:02+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000872s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000627s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 90,10 [ RunTime:0.004490s ]
---------------------------------------------------------------

[2025-08-04T03:36:03+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000774s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000453s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000586s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 100,10 [ RunTime:0.002778s ]
---------------------------------------------------------------

[2025-08-04T03:36:05+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000956s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000439s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000614s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000324s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 110,10 [ RunTime:0.002780s ]
---------------------------------------------------------------

[2025-08-04T03:36:07+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001450s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000680s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000488s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000626s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000299s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 120,10 [ RunTime:0.002999s ]
---------------------------------------------------------------

[2025-08-04T03:36:10+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000830s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000462s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000386s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000620s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 130,10 [ RunTime:0.003050s ]
---------------------------------------------------------------

[2025-08-04T03:36:11+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001004s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000619s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000466s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 140,10 [ RunTime:0.003189s ]
---------------------------------------------------------------

[2025-08-04T03:36:13+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000933s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000646s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 150,10 [ RunTime:0.002839s ]
---------------------------------------------------------------

[2025-08-04T03:36:14+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000938s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000546s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 160,10 [ RunTime:0.002815s ]
---------------------------------------------------------------

[2025-08-04T03:36:15+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001046s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000525s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000591s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000394s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 170,10 [ RunTime:0.002913s ]
---------------------------------------------------------------

[2025-08-04T03:36:16+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000907s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.012841s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000451s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000525s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 180,10 [ RunTime:0.004967s ]
---------------------------------------------------------------

[2025-08-04T03:36:17+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.002899s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000513s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000430s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000659s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 190,10 [ RunTime:0.003032s ]
---------------------------------------------------------------

[2025-08-04T03:36:18+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001280s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000807s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000282s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 200,10 [ RunTime:0.002912s ]
---------------------------------------------------------------

[2025-08-04T03:36:19+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001087s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000520s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000438s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000395s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 210,10 [ RunTime:0.003035s ]
---------------------------------------------------------------

[2025-08-04T03:36:20+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.002814s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000515s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000791s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 220,10 [ RunTime:0.003097s ]
---------------------------------------------------------------

[2025-08-04T03:36:21+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001157s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000536s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 230,10 [ RunTime:0.003422s ]
---------------------------------------------------------------

[2025-08-04T03:36:23+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000989s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000539s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000359s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 950,10 [ RunTime:0.004222s ]
---------------------------------------------------------------

[2025-08-04T03:36:26+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000947s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000635s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 940,10 [ RunTime:0.003841s ]
---------------------------------------------------------------

[2025-08-04T03:36:29+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001053s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000589s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000408s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000770s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 930,10 [ RunTime:0.003809s ]
---------------------------------------------------------------

[2025-08-04T03:36:30+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000941s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000627s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000675s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 920,10 [ RunTime:0.003727s ]
---------------------------------------------------------------

[2025-08-04T03:36:31+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000869s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000708s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000570s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000701s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 910,10 [ RunTime:0.003539s ]
---------------------------------------------------------------

[2025-08-04T03:36:33+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001191s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000852s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000440s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000828s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000434s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 900,10 [ RunTime:0.003611s ]
---------------------------------------------------------------

[2025-08-04T03:36:34+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000987s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 890,10 [ RunTime:0.003509s ]
---------------------------------------------------------------

[2025-08-04T03:36:36+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001379s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000923s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000405s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000792s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000514s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 880,10 [ RunTime:0.004543s ]
---------------------------------------------------------------

[2025-08-04T03:36:40+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000906s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000596s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000418s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 870,10 [ RunTime:0.003465s ]
---------------------------------------------------------------

[2025-08-04T03:36:40+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000833s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000466s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000534s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000570s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 860,10 [ RunTime:0.005455s ]
---------------------------------------------------------------

[2025-08-04T03:36:41+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001498s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001647s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000547s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 850,10 [ RunTime:0.004490s ]
---------------------------------------------------------------

[2025-08-04T03:36:43+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001110s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000496s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000373s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000522s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000271s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 840,10 [ RunTime:0.003231s ]
---------------------------------------------------------------

[2025-08-04T03:36:44+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000979s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 830,10 [ RunTime:0.003528s ]
---------------------------------------------------------------

[2025-08-04T03:36:45+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.002582s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000861s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.001106s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000503s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 820,10 [ RunTime:0.003500s ]
---------------------------------------------------------------

[2025-08-04T03:36:46+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000870s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001260s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.001448s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000497s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000302s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 810,10 [ RunTime:0.003528s ]
---------------------------------------------------------------

[2025-08-04T03:36:48+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000874s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000724s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249808 LIMIT 100 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000996s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000407s ]
---------------------------------------------------------------

[2025-08-04T03:37:23+08:00] ********** GET localhost/manage/bet/TaskClass
[ sql ] [ DB ] CONNECT:[ UseTime:0.000857s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000618s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclass'  AND `state` = 1 [ RunTime:0.000557s ]
---------------------------------------------------------------

[2025-08-04T03:37:23+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001081s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000570s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001097s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000187s ]
---------------------------------------------------------------

[2025-08-04T03:37:23+08:00] ********** POST localhost/manage/bet/taskclass
[ sql ] [ DB ] CONNECT:[ UseTime:0.000774s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000519s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclass'  AND `state` = 1 [ RunTime:0.000427s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000837s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task_class` [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000332s ]
---------------------------------------------------------------

[2025-08-04T03:37:29+08:00] ********** GET localhost/manage/bet/TaskClassEdit?id=27
[ sql ] [ DB ] CONNECT:[ UseTime:0.000882s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000630s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclassedit'  AND `state` = 1 [ RunTime:0.000431s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000674s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000362s ]
---------------------------------------------------------------

[2025-08-04T03:37:29+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001111s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000559s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000461s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000817s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000160s ]
---------------------------------------------------------------

[2025-08-04T03:37:34+08:00] ********** GET localhost/manage/bet/TaskClassEdit?id=28
[ sql ] [ DB ] CONNECT:[ UseTime:0.000870s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000521s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclassedit'  AND `state` = 1 [ RunTime:0.000464s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000604s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 28 LIMIT 1 [ RunTime:0.000379s ]
---------------------------------------------------------------

[2025-08-04T03:37:34+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001087s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000566s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000560s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001237s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000236s ]
---------------------------------------------------------------

[2025-08-04T03:37:38+08:00] ********** GET localhost/manage/bet/TaskClassEdit?id=35
[ sql ] [ DB ] CONNECT:[ UseTime:0.000934s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000582s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclassedit'  AND `state` = 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 35 LIMIT 1 [ RunTime:0.000368s ]
---------------------------------------------------------------

[2025-08-04T03:37:38+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000947s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000538s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000399s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001044s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000235s ]
---------------------------------------------------------------

[2025-08-04T03:37:45+08:00] ********** GET localhost/manage/bet/TaskClassEdit?id=34
[ sql ] [ DB ] CONNECT:[ UseTime:0.000895s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000497s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclassedit'  AND `state` = 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000671s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 34 LIMIT 1 [ RunTime:0.000363s ]
---------------------------------------------------------------

[2025-08-04T03:37:45+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001011s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000519s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000886s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000462s ]
---------------------------------------------------------------

[2025-08-04T03:37:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001182s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249868 LIMIT 100 [ RunTime:0.000304s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000867s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000395s ]
---------------------------------------------------------------

[2025-08-04T03:37:49+08:00] ********** GET localhost/manage/bet/TaskClassEdit?id=33
[ sql ] [ DB ] CONNECT:[ UseTime:0.000996s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000682s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclassedit'  AND `state` = 1 [ RunTime:0.000483s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000649s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 33 LIMIT 1 [ RunTime:0.000317s ]
---------------------------------------------------------------

[2025-08-04T03:37:49+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000939s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000527s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000467s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001144s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000295s ]
---------------------------------------------------------------

[2025-08-04T03:38:29+08:00] ********** GET localhost/manage/bet/TaskClass
[ sql ] [ DB ] CONNECT:[ UseTime:0.001027s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000748s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclass'  AND `state` = 1 [ RunTime:0.000550s ]
---------------------------------------------------------------

[2025-08-04T03:38:29+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001071s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000638s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000411s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000824s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000230s ]
---------------------------------------------------------------

[2025-08-04T03:38:29+08:00] ********** POST localhost/manage/bet/taskclass
[ sql ] [ DB ] CONNECT:[ UseTime:0.001000s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000603s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclass'  AND `state` = 1 [ RunTime:0.000646s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001003s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task_class` [ RunTime:0.000221s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000689s ]
---------------------------------------------------------------

[2025-08-04T03:38:31+08:00] ********** GET localhost/manage/bet/TaskClassEdit?id=27
[ sql ] [ DB ] CONNECT:[ UseTime:0.000826s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000582s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclassedit'  AND `state` = 1 [ RunTime:0.000471s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000614s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000346s ]
---------------------------------------------------------------

[2025-08-04T03:38:32+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000874s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000496s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000522s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000765s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000208s ]
---------------------------------------------------------------

[2025-08-04T03:38:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001151s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249929 LIMIT 100 [ RunTime:0.000235s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001010s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000256s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000304s ]
---------------------------------------------------------------

[2025-08-04T03:39:21+08:00] ********** GET localhost/manage/Bet/financial
[ sql ] [ DB ] CONNECT:[ UseTime:0.001132s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000706s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/financial'  AND `state` = 1 [ RunTime:0.000435s ]
---------------------------------------------------------------

[2025-08-04T03:39:22+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001146s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000767s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000531s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001341s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000310s ]
---------------------------------------------------------------

[2025-08-04T03:39:22+08:00] ********** POST localhost/manage/bet/financial
[ sql ] [ DB ] CONNECT:[ UseTime:0.000995s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/financial'  AND `state` = 1 [ RunTime:0.000362s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001048s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `types` = 1  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT `td`.*,u.username as source_username FROM `ly_trade_details` `td` LEFT JOIN `ly_users` `u` ON `td`.`source_uid`=`u`.`id` WHERE  `types` = 1  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199 ORDER BY `td`.`trade_time` DESC LIMIT 0,10 [ RunTime:0.000680s ]
---------------------------------------------------------------

[2025-08-04T03:39:28+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001275s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000508s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000442s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000526s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000239s ]
---------------------------------------------------------------

[2025-08-04T03:39:28+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001087s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000477s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000442s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000660s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 0,10 [ RunTime:0.003742s ]
---------------------------------------------------------------

[2025-08-04T03:39:28+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001139s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000721s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000407s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001031s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000406s ]
---------------------------------------------------------------

[2025-08-04T03:39:47+08:00] ********** GET localhost/manage/bet/taskTplAdd
[ sql ] [ DB ] CONNECT:[ UseTime:0.000927s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000551s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasktpladd'  AND `state` = 1 [ RunTime:0.000464s ]
---------------------------------------------------------------

[2025-08-04T03:39:48+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001027s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000540s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000696s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001104s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000272s ]
---------------------------------------------------------------

[2025-08-04T03:39:48+08:00] ********** POST localhost/manage/bet/taskModelList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001036s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001013s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskmodellist'  AND `state` = 1 [ RunTime:0.000714s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_tpl` [ RunTime:0.004954s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task_tpl` INNER JOIN `ly_task_class` ON `ly_task_tpl`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT `ly_task_tpl`.*,`ly_task_class`.`group_name` FROM `ly_task_tpl` INNER JOIN `ly_task_class` ON `ly_task_tpl`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 0,10 [ RunTime:0.000734s ]
---------------------------------------------------------------

[2025-08-04T03:39:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000986s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000492s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249990 LIMIT 100 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000967s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000300s ]
---------------------------------------------------------------

[2025-08-04T03:39:52+08:00] ********** GET localhost/manage/bet/TaskClass
[ sql ] [ DB ] CONNECT:[ UseTime:0.000996s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000822s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclass'  AND `state` = 1 [ RunTime:0.000707s ]
---------------------------------------------------------------

[2025-08-04T03:39:52+08:00] ********** POST localhost/manage/bet/taskclass
[ sql ] [ DB ] CONNECT:[ UseTime:0.000961s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000589s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclass'  AND `state` = 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000811s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task_class` [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000325s ]
---------------------------------------------------------------

[2025-08-04T03:39:52+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001178s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000481s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000630s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000965s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000227s ]
---------------------------------------------------------------

[2025-08-04T03:39:55+08:00] ********** GET localhost/manage/bet/TaskClassEdit?id=35
[ sql ] [ DB ] CONNECT:[ UseTime:0.000800s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000564s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/taskclassedit'  AND `state` = 1 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000570s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 35 LIMIT 1 [ RunTime:0.000299s ]
---------------------------------------------------------------

[2025-08-04T03:39:55+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000913s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000689s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000486s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000914s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000292s ]
---------------------------------------------------------------

[2025-08-04T03:40:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001151s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000604s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754250050 LIMIT 100 [ RunTime:0.000255s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001006s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000207s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000240s ]
---------------------------------------------------------------

[2025-08-04T03:41:21+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001099s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000527s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000536s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` ORDER BY `num` ASC [ RunTime:0.000347s ]
---------------------------------------------------------------

[2025-08-04T03:41:21+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001165s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000633s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000469s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000726s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 0,10 [ RunTime:0.003529s ]
---------------------------------------------------------------

[2025-08-04T03:41:21+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000864s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000809s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000398s ]
---------------------------------------------------------------

[2025-08-04T03:41:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001180s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001265s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754250111 LIMIT 100 [ RunTime:0.000444s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001031s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.001135s ]
---------------------------------------------------------------

[2025-08-04T03:42:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000987s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000613s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754250172 LIMIT 100 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001489s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000363s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000382s ]
---------------------------------------------------------------

[2025-08-04T03:43:15+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000993s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000605s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000373s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000535s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` ORDER BY `num` ASC [ RunTime:0.000302s ]
---------------------------------------------------------------

[2025-08-04T03:43:15+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001017s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000448s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000453s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 0,10 [ RunTime:0.003648s ]
---------------------------------------------------------------

[2025-08-04T03:43:15+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001050s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000613s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000434s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001100s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000446s ]
---------------------------------------------------------------

[2025-08-04T03:43:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001020s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754250232 LIMIT 100 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000976s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000213s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000256s ]
