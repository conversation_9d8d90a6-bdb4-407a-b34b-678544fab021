---------------------------------------------------------------

[2025-08-04T00:00:36+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001164s ] mysql:host=mysql;dbname=di<PERSON><PERSON><PERSON>;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000500s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236836 LIMIT 100 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000833s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000225s ]
---------------------------------------------------------------

[2025-08-04T00:01:36+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001196s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000844s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236896 LIMIT 100 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001100s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000219s ]
---------------------------------------------------------------

[2025-08-04T00:02:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001080s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000691s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236958 LIMIT 100 [ RunTime:0.000417s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001094s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000201s ]
---------------------------------------------------------------

[2025-08-04T00:03:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001095s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237019 LIMIT 100 [ RunTime:0.000215s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000981s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000231s ]
---------------------------------------------------------------

[2025-08-04T00:04:42+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000917s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237082 LIMIT 100 [ RunTime:0.000205s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001024s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000342s ]
---------------------------------------------------------------

[2025-08-04T00:05:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000972s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000724s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237143 LIMIT 100 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000894s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000463s ]
---------------------------------------------------------------

[2025-08-04T00:06:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000913s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000497s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237205 LIMIT 100 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001048s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000223s ]
---------------------------------------------------------------

[2025-08-04T00:07:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001126s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237267 LIMIT 100 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000997s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000232s ]
---------------------------------------------------------------

[2025-08-04T00:08:48+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001075s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237327 LIMIT 100 [ RunTime:0.000237s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000894s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000198s ]
---------------------------------------------------------------

[2025-08-04T00:09:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001011s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000560s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237389 LIMIT 100 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000982s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000362s ]
---------------------------------------------------------------

[2025-08-04T00:10:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001023s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000498s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237451 LIMIT 100 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000846s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000240s ]
---------------------------------------------------------------

[2025-08-04T00:11:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001321s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000616s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237514 LIMIT 100 [ RunTime:0.000193s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000768s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000197s ]
---------------------------------------------------------------

[2025-08-04T00:12:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000893s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000476s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237576 LIMIT 100 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001186s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000267s ]
---------------------------------------------------------------

[2025-08-04T00:13:59+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001067s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000503s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237639 LIMIT 100 [ RunTime:0.000208s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000864s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000211s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000247s ]
---------------------------------------------------------------

[2025-08-04T00:15:03+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000935s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000638s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237702 LIMIT 100 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000798s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000602s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000461s ]
---------------------------------------------------------------

[2025-08-04T00:16:06+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001309s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000877s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237765 LIMIT 100 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000849s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000419s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000369s ]
---------------------------------------------------------------

[2025-08-04T00:17:07+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001238s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000790s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237827 LIMIT 100 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000951s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000300s ]
---------------------------------------------------------------

[2025-08-04T00:18:09+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001136s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000822s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237889 LIMIT 100 [ RunTime:0.000255s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000916s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000274s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000264s ]
---------------------------------------------------------------

[2025-08-04T00:19:10+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001046s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000585s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754237950 LIMIT 100 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000913s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000238s ]
---------------------------------------------------------------

[2025-08-04T00:20:12+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001042s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000658s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238012 LIMIT 100 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000443s ]
---------------------------------------------------------------

[2025-08-04T00:21:14+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001050s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000540s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238074 LIMIT 100 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000941s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000225s ]
---------------------------------------------------------------

[2025-08-04T00:22:16+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000926s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000718s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238136 LIMIT 100 [ RunTime:0.000214s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000993s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000285s ]
---------------------------------------------------------------

[2025-08-04T00:23:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000847s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238198 LIMIT 100 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000949s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000208s ]
---------------------------------------------------------------

[2025-08-04T00:24:20+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001211s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000801s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238259 LIMIT 100 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000860s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000227s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000275s ]
---------------------------------------------------------------

[2025-08-04T00:25:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001965s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000850s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238321 LIMIT 100 [ RunTime:0.000396s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001013s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000287s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000245s ]
---------------------------------------------------------------

[2025-08-04T00:26:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001064s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000714s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238383 LIMIT 100 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000862s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000298s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000306s ]
---------------------------------------------------------------

[2025-08-04T00:27:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001030s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000792s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238444 LIMIT 100 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000928s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000199s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000296s ]
---------------------------------------------------------------

[2025-08-04T00:28:25+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001317s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238505 LIMIT 100 [ RunTime:0.000298s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000753s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000219s ]
---------------------------------------------------------------

[2025-08-04T00:29:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000865s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238567 LIMIT 100 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000855s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000292s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000206s ]
---------------------------------------------------------------

[2025-08-04T00:30:30+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001061s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000542s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238629 LIMIT 100 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000976s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000268s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000718s ]
---------------------------------------------------------------

[2025-08-04T00:31:31+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001064s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000870s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238691 LIMIT 100 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000791s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000277s ]
---------------------------------------------------------------

[2025-08-04T00:32:32+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001890s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238752 LIMIT 100 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001051s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000262s ]
---------------------------------------------------------------

[2025-08-04T00:33:27+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001227s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001430s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.001150s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000905s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238807 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.001050s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000679s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001182s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000878s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001151s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.001047s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000580s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000442s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000551s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000450s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000561s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000858s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000497s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000461s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000394s ]
---------------------------------------------------------------

[2025-08-04T00:33:28+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001580s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000768s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000895s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000517s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001187s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000722s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001006s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000718s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000951s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000699s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000951s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000373s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000344s ]
---------------------------------------------------------------

[2025-08-04T00:33:28+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000725s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000836s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000244s ]
---------------------------------------------------------------

[2025-08-04T00:33:28+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.005136s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000791s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000429s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238808 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000705s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000551s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000573s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000367s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000261s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000668s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000315s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000256s ]
---------------------------------------------------------------

[2025-08-04T00:33:28+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000772s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000652s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000558s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238808 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000408s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000819s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000467s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000944s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.001085s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000821s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000981s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.001126s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000706s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.001013s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001139s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000427s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000781s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000263s ]
---------------------------------------------------------------

[2025-08-04T00:33:29+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000902s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000647s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000674s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238808 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000438s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000625s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000556s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000387s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000417s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000753s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000298s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000276s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000391s ]
---------------------------------------------------------------

[2025-08-04T00:33:33+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001122s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000631s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238813 LIMIT 100 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000983s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000379s ]
---------------------------------------------------------------

[2025-08-04T00:34:36+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001910s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001178s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238875 LIMIT 100 [ RunTime:0.000552s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000868s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000282s ]
---------------------------------------------------------------

[2025-08-04T00:35:27+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001189s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000870s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000507s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000530s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238927 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000529s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000531s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000546s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000480s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000456s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000452s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000227s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000282s ]
---------------------------------------------------------------

[2025-08-04T00:35:28+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.009793s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000974s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000602s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238928 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000598s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001215s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000452s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000997s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000743s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000760s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000710s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000528s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000464s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001318s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000287s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000404s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000461s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000816s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000507s ]
---------------------------------------------------------------

[2025-08-04T00:35:28+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001312s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000789s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000362s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000872s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000744s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001422s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000929s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000785s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000590s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001253s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000934s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000478s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000333s ]
---------------------------------------------------------------

[2025-08-04T00:35:28+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000979s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000757s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000213s ]
---------------------------------------------------------------

[2025-08-04T00:35:28+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000812s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000818s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000387s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000456s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238928 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000340s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000837s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000560s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000275s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000408s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000515s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000203s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000689s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000443s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000244s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000230s ]
---------------------------------------------------------------

[2025-08-04T00:35:32+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001146s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002084s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000919s ]
---------------------------------------------------------------

[2025-08-04T00:35:32+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.009723s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.001301s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000396s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000705s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000556s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000344s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001128s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000423s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000456s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000627s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000214s ]
---------------------------------------------------------------

[2025-08-04T00:35:32+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.013716s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000684s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238932 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000287s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000767s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000584s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000396s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000657s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000382s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000397s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000283s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000764s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000383s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000294s ]
---------------------------------------------------------------

[2025-08-04T00:35:33+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001068s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000840s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000287s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000422s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238932 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000353s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000501s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000902s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000552s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000656s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000439s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000339s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000409s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000609s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000273s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000778s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000354s ]
---------------------------------------------------------------

[2025-08-04T00:35:37+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001281s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238937 LIMIT 100 [ RunTime:0.000282s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001022s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000384s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000507s ]
---------------------------------------------------------------

[2025-08-04T00:36:23+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000950s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000856s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000275s ]
---------------------------------------------------------------

[2025-08-04T00:36:23+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.010445s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.001031s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000404s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000777s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000687s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000370s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000695s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000514s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000699s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000547s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000718s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000338s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000257s ]
---------------------------------------------------------------

[2025-08-04T00:36:23+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000835s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000610s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000672s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238983 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000387s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000669s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000777s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000457s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000451s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000687s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000997s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000567s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000283s ]
---------------------------------------------------------------

[2025-08-04T00:36:23+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001028s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000926s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000752s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238983 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000467s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000468s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000624s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000359s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000302s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000726s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000235s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000225s ]
---------------------------------------------------------------

[2025-08-04T00:36:26+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001790s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001875s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000458s ]
---------------------------------------------------------------

[2025-08-04T00:36:27+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.011280s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.001216s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000427s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000955s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000421s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000780s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000661s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000655s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000371s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000653s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000298s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000352s ]
---------------------------------------------------------------

[2025-08-04T00:36:27+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.007966s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000813s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000377s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000648s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238986 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000667s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000665s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000578s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000652s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000373s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000279s ]
---------------------------------------------------------------

[2025-08-04T00:36:27+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000868s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000678s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000495s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754238987 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000233s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000749s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000525s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000597s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000364s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000910s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000362s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000456s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001137s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000576s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000395s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.001034s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000552s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000724s ]
---------------------------------------------------------------

[2025-08-04T00:36:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000823s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000462s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754238997 LIMIT 100 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000973s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000474s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000234s ]
---------------------------------------------------------------

[2025-08-04T00:36:54+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000874s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000738s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000479s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754239014 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000452s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000453s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000806s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000618s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000397s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000590s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000556s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000439s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000421s ]
---------------------------------------------------------------

[2025-08-04T00:36:56+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001647s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000936s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.001274s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000690s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754239016 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.002152s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000434s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000605s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001191s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000530s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000462s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.001681s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000387s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000494s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000859s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000430s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000328s ]
---------------------------------------------------------------

[2025-08-04T00:37:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000830s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000482s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239058 LIMIT 100 [ RunTime:0.000376s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000892s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000429s ]
---------------------------------------------------------------

[2025-08-04T00:38:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001171s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000778s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239119 LIMIT 100 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001012s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000611s ]
---------------------------------------------------------------

[2025-08-04T00:39:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001082s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000785s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239180 LIMIT 100 [ RunTime:0.000286s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001028s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000428s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000315s ]
---------------------------------------------------------------

[2025-08-04T00:39:43+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000891s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000765s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000563s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000809s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754239183 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000528s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000815s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000441s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000899s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000492s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000426s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000412s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000536s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000633s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000326s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000291s ]
---------------------------------------------------------------

[2025-08-04T00:39:57+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000787s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000727s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000354s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000460s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754239197 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000724s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000534s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000498s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000273s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000852s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000468s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000320s ]
---------------------------------------------------------------

[2025-08-04T00:39:57+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.005184s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000235s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000712s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000379s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000966s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000578s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000635s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000505s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000923s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000531s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000330s ]
---------------------------------------------------------------

[2025-08-04T00:39:57+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.003452s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000923s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000368s ]
---------------------------------------------------------------

[2025-08-04T00:39:57+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000833s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000718s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000292s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000517s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754239197 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000602s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000722s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000408s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000733s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000393s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000443s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000364s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000700s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000458s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000376s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000452s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000283s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000320s ]
---------------------------------------------------------------

[2025-08-04T00:39:57+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000843s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000616s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000756s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754239197 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000732s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000808s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000459s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000440s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000935s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000292s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000375s ]
---------------------------------------------------------------

[2025-08-04T00:40:43+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001027s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001307s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239242 LIMIT 100 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000900s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000327s ]
---------------------------------------------------------------

[2025-08-04T00:41:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001085s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000686s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239305 LIMIT 100 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000871s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000423s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000333s ]
---------------------------------------------------------------

[2025-08-04T00:42:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000962s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000758s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239366 LIMIT 100 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000900s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000325s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000208s ]
---------------------------------------------------------------

[2025-08-04T00:43:48+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001063s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000813s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239428 LIMIT 100 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000911s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000287s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000245s ]
---------------------------------------------------------------

[2025-08-04T00:44:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001086s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000483s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239490 LIMIT 100 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001034s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000282s ]
---------------------------------------------------------------

[2025-08-04T00:45:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000998s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000518s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239551 LIMIT 100 [ RunTime:0.000191s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000997s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000220s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000206s ]
---------------------------------------------------------------

[2025-08-04T00:46:54+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001016s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000561s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239613 LIMIT 100 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000885s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000416s ]
---------------------------------------------------------------

[2025-08-04T00:47:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000897s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000517s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239675 LIMIT 100 [ RunTime:0.000235s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000806s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000334s ]
---------------------------------------------------------------

[2025-08-04T00:48:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001257s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000872s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239737 LIMIT 100 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000851s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000305s ]
---------------------------------------------------------------

[2025-08-04T00:49:58+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000971s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239798 LIMIT 100 [ RunTime:0.000354s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000941s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000209s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000252s ]
---------------------------------------------------------------

[2025-08-04T00:51:00+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000975s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000590s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239859 LIMIT 100 [ RunTime:0.000393s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000891s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000372s ]
---------------------------------------------------------------

[2025-08-04T00:52:01+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000926s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000502s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239921 LIMIT 100 [ RunTime:0.000208s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000788s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000327s ]
---------------------------------------------------------------

[2025-08-04T00:53:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000946s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000690s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754239982 LIMIT 100 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000872s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000195s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000223s ]
---------------------------------------------------------------

[2025-08-04T00:54:04+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000905s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000785s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240044 LIMIT 100 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000874s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000411s ]
---------------------------------------------------------------

[2025-08-04T00:55:05+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000810s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240105 LIMIT 100 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000787s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000256s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000612s ]
---------------------------------------------------------------

[2025-08-04T00:56:08+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001025s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000566s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240167 LIMIT 100 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000926s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000221s ]
---------------------------------------------------------------

[2025-08-04T00:57:09+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000879s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000516s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240229 LIMIT 100 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000831s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000254s ]
---------------------------------------------------------------

[2025-08-04T00:58:10+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000990s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000574s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240290 LIMIT 100 [ RunTime:0.000238s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000946s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000498s ]
---------------------------------------------------------------

[2025-08-04T00:59:12+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001509s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000887s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240351 LIMIT 100 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000821s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000227s ]
---------------------------------------------------------------

[2025-08-04T01:00:13+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001125s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000597s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240412 LIMIT 100 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000988s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000275s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000271s ]
---------------------------------------------------------------

[2025-08-04T01:01:15+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001506s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000629s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240474 LIMIT 100 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001016s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000291s ]
---------------------------------------------------------------

[2025-08-04T01:02:16+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001169s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240535 LIMIT 100 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000761s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000455s ]
---------------------------------------------------------------

[2025-08-04T01:03:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000916s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000497s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240597 LIMIT 100 [ RunTime:0.000478s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000782s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000435s ]
---------------------------------------------------------------

[2025-08-04T01:04:20+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000999s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000610s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240660 LIMIT 100 [ RunTime:0.000429s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000864s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000685s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000307s ]
---------------------------------------------------------------

[2025-08-04T01:05:21+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001139s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000590s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240721 LIMIT 100 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000864s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000183s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000263s ]
---------------------------------------------------------------

[2025-08-04T01:06:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000913s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000574s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240782 LIMIT 100 [ RunTime:0.000303s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000901s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000288s ]
---------------------------------------------------------------

[2025-08-04T01:07:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001008s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000610s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240844 LIMIT 100 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000885s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000248s ]
---------------------------------------------------------------

[2025-08-04T01:08:26+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001407s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240905 LIMIT 100 [ RunTime:0.000524s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000804s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000231s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000315s ]
---------------------------------------------------------------

[2025-08-04T01:09:29+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001003s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000579s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754240968 LIMIT 100 [ RunTime:0.000199s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001082s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000432s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000383s ]
---------------------------------------------------------------

[2025-08-04T01:10:30+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001192s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000516s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241030 LIMIT 100 [ RunTime:0.000204s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001242s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000191s ]
---------------------------------------------------------------

[2025-08-04T01:11:33+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000897s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000585s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241092 LIMIT 100 [ RunTime:0.000244s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000855s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000251s ]
---------------------------------------------------------------

[2025-08-04T01:12:34+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001107s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000518s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241153 LIMIT 100 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000921s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000232s ]
---------------------------------------------------------------

[2025-08-04T01:13:36+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000901s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000521s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241216 LIMIT 100 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000926s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000304s ]
---------------------------------------------------------------

[2025-08-04T01:14:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001155s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000873s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241277 LIMIT 100 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000929s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000215s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000395s ]
---------------------------------------------------------------

[2025-08-04T01:15:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002145s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000849s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241339 LIMIT 100 [ RunTime:0.000326s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000962s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000353s ]
---------------------------------------------------------------

[2025-08-04T01:16:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000875s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000859s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241400 LIMIT 100 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000938s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000495s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000557s ]
---------------------------------------------------------------

[2025-08-04T01:17:42+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000892s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000490s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241461 LIMIT 100 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001103s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000244s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000452s ]
---------------------------------------------------------------

[2025-08-04T01:18:43+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001368s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241523 LIMIT 100 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001211s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000420s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000317s ]
---------------------------------------------------------------

[2025-08-04T01:19:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000895s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000507s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241584 LIMIT 100 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001090s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000245s ]
---------------------------------------------------------------

[2025-08-04T01:20:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001010s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000728s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241645 LIMIT 100 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001027s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000284s ]
---------------------------------------------------------------

[2025-08-04T01:21:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001026s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000542s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241706 LIMIT 100 [ RunTime:0.000364s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001136s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000211s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000392s ]
---------------------------------------------------------------

[2025-08-04T01:22:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001121s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000725s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241768 LIMIT 100 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001145s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000491s ]
---------------------------------------------------------------

[2025-08-04T01:23:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001004s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000980s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241829 LIMIT 100 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000788s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000317s ]
---------------------------------------------------------------

[2025-08-04T01:24:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001050s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000697s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241892 LIMIT 100 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000925s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000195s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000400s ]
---------------------------------------------------------------

[2025-08-04T01:25:53+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001054s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000554s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754241953 LIMIT 100 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000797s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000450s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000497s ]
---------------------------------------------------------------

[2025-08-04T01:26:54+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001125s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000641s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242014 LIMIT 100 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001007s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000386s ]
---------------------------------------------------------------

[2025-08-04T01:27:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000832s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000529s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242075 LIMIT 100 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001054s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000261s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000223s ]
---------------------------------------------------------------

[2025-08-04T01:28:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000926s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000497s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242136 LIMIT 100 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000811s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000335s ]
---------------------------------------------------------------

[2025-08-04T01:29:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001331s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000838s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242197 LIMIT 100 [ RunTime:0.000422s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001225s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000327s ]
---------------------------------------------------------------

[2025-08-04T01:30:59+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001452s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000757s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242259 LIMIT 100 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000970s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000326s ]
---------------------------------------------------------------

[2025-08-04T01:32:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001150s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242321 LIMIT 100 [ RunTime:0.000255s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001268s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000428s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000372s ]
---------------------------------------------------------------

[2025-08-04T01:33:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001189s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000593s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242382 LIMIT 100 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000799s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000375s ]
---------------------------------------------------------------

[2025-08-04T01:34:03+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001098s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242443 LIMIT 100 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001084s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000240s ]
---------------------------------------------------------------

[2025-08-04T01:35:04+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000946s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000688s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242504 LIMIT 100 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000932s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000887s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000358s ]
---------------------------------------------------------------

[2025-08-04T01:35:49+08:00] ********** GET localhost/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001158s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000692s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.001207s ]
---------------------------------------------------------------

[2025-08-04T01:35:49+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000988s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000887s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000463s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001196s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.002174s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name,users.vip_level,uv.name as vip_name,uv.grade as vip_grade,uv.etime as vip_etime,ug.name as grade_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` LEFT JOIN `ly_user_vip` `uv` ON `ly_user_withdrawals`.`uid`=uv.uid AND uv.state = 1 LEFT JOIN `ly_user_grade` `ug` ON `users`.`vip_level`=`ug`.`grade` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.002802s ]
---------------------------------------------------------------

[2025-08-04T01:35:49+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.001068s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000784s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000524s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000742s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000560s ]
---------------------------------------------------------------

[2025-08-04T01:36:06+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001311s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000656s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242565 LIMIT 100 [ RunTime:0.000225s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002130s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000686s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000610s ]
---------------------------------------------------------------

[2025-08-04T01:37:07+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001134s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000663s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242627 LIMIT 100 [ RunTime:0.000332s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000910s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000197s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000227s ]
---------------------------------------------------------------

[2025-08-04T01:37:54+08:00] ********** GET localhost/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000841s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000693s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000419s ]
---------------------------------------------------------------

[2025-08-04T01:37:54+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.001318s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000607s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000604s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000560s ]
---------------------------------------------------------------

[2025-08-04T01:37:54+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000825s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000857s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000503s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000350s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name,users.vip_level,uv.name as vip_name,uv.grade as vip_grade,uv.etime as vip_etime,ug.name as grade_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` LEFT JOIN `ly_user_vip` `uv` ON `ly_user_withdrawals`.`uid`=uv.uid AND uv.state = 1 LEFT JOIN `ly_user_grade` `ug` ON `users`.`vip_level`=`ug`.`grade` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.000954s ]
---------------------------------------------------------------

[2025-08-04T01:38:07+08:00] ********** GET localhost/manage/index?login=up2025
[ sql ] [ DB ] CONNECT:[ UseTime:0.000958s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000714s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000300s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000624s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000729s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000762s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 2  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000563s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 4  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 5  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000370s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 3  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000442s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 331  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 347  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000341s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 363  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000325s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001168s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN 1754236800 AND 1754242687 [ RunTime:0.000409s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000867s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9 [ RunTime:0.000431s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN 1754236800 AND 1754242687 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000537s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN 1754236800 AND 1754242687 [ RunTime:0.000154s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000174s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000831s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000503s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754242687 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754242687 [ RunTime:0.000204s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000204s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000469s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN 1754236800 AND 1754242687 [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754236799 [ RunTime:0.000493s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN 1754236800 AND 1754242687 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754236799 [ RunTime:0.000502s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000744s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_total` [ RunTime:0.000652s ]
---------------------------------------------------------------

[2025-08-04T01:38:08+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000993s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000539s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242688 LIMIT 100 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000968s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000337s ]
---------------------------------------------------------------

[2025-08-04T01:38:10+08:00] ********** GET localhost/manage/User/userlist
[ sql ] [ DB ] CONNECT:[ UseTime:0.000918s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000601s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'User/userlist'  AND `state` = 1 [ RunTime:0.000478s ]
---------------------------------------------------------------

[2025-08-04T01:38:10+08:00] ********** POST localhost/manage/user/userList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000898s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001279s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'User/userlist'  AND `state` = 1 [ RunTime:0.000512s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000605s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` INNER JOIN `ly_user_total` `user_total` ON `ly_users`.`id`=`user_total`.`uid` [ RunTime:0.000239s ]
[ sql ] [ SQL ] SELECT `ly_users`.*,`user_total`.`balance`,`user_total`.`total_balance` FROM `ly_users` INNER JOIN `ly_user_total` `user_total` ON `ly_users`.`id`=`user_total`.`uid` ORDER BY `reg_time` DESC LIMIT 0,10 [ RunTime:0.000522s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001975s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000227s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000213s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000202s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000256s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000296s ]
---------------------------------------------------------------

[2025-08-04T01:39:09+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000941s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000663s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242749 LIMIT 100 [ RunTime:0.000218s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001048s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000426s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000566s ]
---------------------------------------------------------------

[2025-08-04T01:39:26+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000872s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000694s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000272s ]
---------------------------------------------------------------

[2025-08-04T01:39:26+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.013872s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000789s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000379s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001054s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754242766 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000783s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000442s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000618s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000598s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000668s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000886s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000393s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001012s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000272s ]
---------------------------------------------------------------

[2025-08-04T01:39:26+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.004068s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000715s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000375s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000662s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000399s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000914s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000459s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000418s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000499s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000299s ]
---------------------------------------------------------------

[2025-08-04T01:39:26+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000847s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000696s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000513s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754242766 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000552s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000705s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000878s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000326s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000276s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000819s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000656s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000261s ]
---------------------------------------------------------------

[2025-08-04T01:39:26+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000823s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000688s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000383s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000497s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000897s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= 1754236800  AND `add_time` <= 1754323199 [ RunTime:0.000325s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000953s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= 1754236800 )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000324s ]
---------------------------------------------------------------

[2025-08-04T01:39:29+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000868s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000593s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000528s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754242769 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000502s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000814s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000675s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000325s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000211s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000419s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000998s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000367s ]
---------------------------------------------------------------

[2025-08-04T01:40:10+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000961s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000673s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242810 LIMIT 100 [ RunTime:0.000302s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000916s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000247s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000357s ]
---------------------------------------------------------------

[2025-08-04T01:41:12+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001060s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000508s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242871 LIMIT 100 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001027s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000308s ]
---------------------------------------------------------------

[2025-08-04T01:42:13+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000831s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242932 LIMIT 100 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001000s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000237s ]
---------------------------------------------------------------

[2025-08-04T01:43:15+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000988s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754242994 LIMIT 100 [ RunTime:0.000244s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000921s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000349s ]
---------------------------------------------------------------

[2025-08-04T01:44:17+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001058s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000526s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243056 LIMIT 100 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001294s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000370s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000291s ]
---------------------------------------------------------------

[2025-08-04T01:45:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000970s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243117 LIMIT 100 [ RunTime:0.000235s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001063s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000200s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000341s ]
---------------------------------------------------------------

[2025-08-04T01:46:19+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001119s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000553s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243179 LIMIT 100 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000892s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000521s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000317s ]
---------------------------------------------------------------

[2025-08-04T01:47:19+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000943s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000686s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243239 LIMIT 100 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000919s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000261s ]
---------------------------------------------------------------

[2025-08-04T01:48:21+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000979s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000614s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243301 LIMIT 100 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000859s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000256s ]
---------------------------------------------------------------

[2025-08-04T01:49:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001209s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000619s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243362 LIMIT 100 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000818s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000208s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000365s ]
---------------------------------------------------------------

[2025-08-04T01:50:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000986s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243424 LIMIT 100 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000915s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000261s ]
---------------------------------------------------------------

[2025-08-04T01:51:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001041s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000905s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243486 LIMIT 100 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000875s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000384s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000258s ]
---------------------------------------------------------------

[2025-08-04T01:52:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000941s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000604s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243547 LIMIT 100 [ RunTime:0.000304s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001143s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000435s ]
---------------------------------------------------------------

[2025-08-04T01:53:29+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001018s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000585s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243608 LIMIT 100 [ RunTime:0.000354s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001063s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000195s ]
---------------------------------------------------------------

[2025-08-04T01:54:30+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001185s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000640s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243670 LIMIT 100 [ RunTime:0.000201s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001011s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000423s ]
---------------------------------------------------------------

[2025-08-04T01:55:32+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000798s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000553s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243731 LIMIT 100 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000997s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000339s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000244s ]
---------------------------------------------------------------

[2025-08-04T01:56:34+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000975s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000677s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243794 LIMIT 100 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000851s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000228s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000337s ]
---------------------------------------------------------------

[2025-08-04T01:57:36+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000979s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000576s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243855 LIMIT 100 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000828s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000182s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000261s ]
---------------------------------------------------------------

[2025-08-04T01:58:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000940s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243917 LIMIT 100 [ RunTime:0.000236s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000844s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000191s ]
---------------------------------------------------------------

[2025-08-04T01:59:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001345s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000592s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754243978 LIMIT 100 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000936s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000527s ]
---------------------------------------------------------------

[2025-08-04T02:00:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001046s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244039 LIMIT 100 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000931s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000315s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000240s ]
---------------------------------------------------------------

[2025-08-04T02:01:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001062s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244100 LIMIT 100 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001102s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000384s ]
---------------------------------------------------------------

[2025-08-04T02:02:41+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001011s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000582s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244161 LIMIT 100 [ RunTime:0.000320s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001003s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000207s ]
---------------------------------------------------------------

[2025-08-04T02:03:42+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000901s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244222 LIMIT 100 [ RunTime:0.000283s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001042s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000241s ]
---------------------------------------------------------------

[2025-08-04T02:04:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000914s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000495s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244283 LIMIT 100 [ RunTime:0.000193s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001170s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000249s ]
---------------------------------------------------------------

[2025-08-04T02:05:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001248s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000597s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244345 LIMIT 100 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000982s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000201s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000390s ]
---------------------------------------------------------------

[2025-08-04T02:06:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000889s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000466s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244406 LIMIT 100 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000848s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000299s ]
---------------------------------------------------------------

[2025-08-04T02:07:48+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000889s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000514s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244468 LIMIT 100 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000732s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000182s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000372s ]
---------------------------------------------------------------

[2025-08-04T02:08:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000914s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000535s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244530 LIMIT 100 [ RunTime:0.000199s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000956s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000237s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000292s ]
---------------------------------------------------------------

[2025-08-04T02:09:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001051s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000605s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244592 LIMIT 100 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001057s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000314s ]
---------------------------------------------------------------

[2025-08-04T02:10:54+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001364s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000567s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244653 LIMIT 100 [ RunTime:0.000235s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000879s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000262s ]
---------------------------------------------------------------

[2025-08-04T02:11:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000938s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000586s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244715 LIMIT 100 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000855s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000220s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000411s ]
---------------------------------------------------------------

[2025-08-04T02:12:58+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000892s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000682s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244777 LIMIT 100 [ RunTime:0.000236s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000780s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000417s ]
---------------------------------------------------------------

[2025-08-04T02:13:58+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000912s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000572s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244838 LIMIT 100 [ RunTime:0.000359s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001014s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000231s ]
---------------------------------------------------------------

[2025-08-04T02:14:59+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001221s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244899 LIMIT 100 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001052s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000277s ]
---------------------------------------------------------------

[2025-08-04T02:16:01+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000948s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000733s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754244961 LIMIT 100 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001026s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000526s ]
---------------------------------------------------------------

[2025-08-04T02:17:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001186s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000721s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245022 LIMIT 100 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001050s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000493s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000269s ]
---------------------------------------------------------------

[2025-08-04T02:18:03+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000931s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000760s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245082 LIMIT 100 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000900s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000269s ]
---------------------------------------------------------------

[2025-08-04T02:19:04+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002496s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000724s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245143 LIMIT 100 [ RunTime:0.000531s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000816s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000377s ]
---------------------------------------------------------------

[2025-08-04T02:20:05+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000879s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000491s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245205 LIMIT 100 [ RunTime:0.000231s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001282s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000189s ]
---------------------------------------------------------------

[2025-08-04T02:21:07+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000870s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000492s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245266 LIMIT 100 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000805s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000255s ]
---------------------------------------------------------------

[2025-08-04T02:22:09+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000817s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245329 LIMIT 100 [ RunTime:0.000551s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000923s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000448s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000306s ]
---------------------------------------------------------------

[2025-08-04T02:23:12+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001188s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245391 LIMIT 100 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000991s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000224s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000382s ]
---------------------------------------------------------------

[2025-08-04T02:24:13+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000976s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000488s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245453 LIMIT 100 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000796s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000353s ]
---------------------------------------------------------------

[2025-08-04T02:25:15+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001016s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000766s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245514 LIMIT 100 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000895s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000409s ]
---------------------------------------------------------------

[2025-08-04T02:26:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000901s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000691s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245577 LIMIT 100 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000955s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000388s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000371s ]
---------------------------------------------------------------

[2025-08-04T02:27:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000843s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000796s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245638 LIMIT 100 [ RunTime:0.000425s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001002s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000432s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000223s ]
---------------------------------------------------------------

[2025-08-04T02:28:21+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001176s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000694s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245700 LIMIT 100 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000973s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000468s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000286s ]
---------------------------------------------------------------

[2025-08-04T02:29:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000813s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000638s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245762 LIMIT 100 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000936s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000238s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000332s ]
---------------------------------------------------------------

[2025-08-04T02:30:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000949s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000767s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245824 LIMIT 100 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000852s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000275s ]
---------------------------------------------------------------

[2025-08-04T02:31:25+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000936s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000461s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245885 LIMIT 100 [ RunTime:0.000244s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000899s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000273s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000256s ]
---------------------------------------------------------------

[2025-08-04T02:32:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000946s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000601s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754245947 LIMIT 100 [ RunTime:0.000353s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001065s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000467s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000363s ]
---------------------------------------------------------------

[2025-08-04T02:33:28+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001036s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000711s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246008 LIMIT 100 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001019s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000256s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000400s ]
---------------------------------------------------------------

[2025-08-04T02:34:30+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001031s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000536s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246070 LIMIT 100 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001007s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000280s ]
---------------------------------------------------------------

[2025-08-04T02:35:32+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000849s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000476s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246131 LIMIT 100 [ RunTime:0.000237s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001222s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000227s ]
---------------------------------------------------------------

[2025-08-04T02:36:34+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001107s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000811s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246193 LIMIT 100 [ RunTime:0.000339s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000864s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000239s ]
---------------------------------------------------------------

[2025-08-04T02:37:35+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000919s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000640s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246254 LIMIT 100 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000782s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000438s ]
---------------------------------------------------------------

[2025-08-04T02:38:37+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001210s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246316 LIMIT 100 [ RunTime:0.000367s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000936s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000805s ]
---------------------------------------------------------------

[2025-08-04T02:39:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000908s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000500s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246378 LIMIT 100 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001384s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000764s ]
---------------------------------------------------------------

[2025-08-04T02:40:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000961s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246439 LIMIT 100 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000810s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000218s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000379s ]
---------------------------------------------------------------

[2025-08-04T02:41:41+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000873s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000498s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246501 LIMIT 100 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001392s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000213s ]
---------------------------------------------------------------

[2025-08-04T02:42:42+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001054s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000844s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246562 LIMIT 100 [ RunTime:0.000244s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001031s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000298s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000300s ]
---------------------------------------------------------------

[2025-08-04T02:43:43+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001062s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000681s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246623 LIMIT 100 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001127s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000287s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000357s ]
---------------------------------------------------------------

[2025-08-04T02:44:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001050s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000534s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246684 LIMIT 100 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000995s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000262s ]
---------------------------------------------------------------

[2025-08-04T02:45:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000875s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000684s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246745 LIMIT 100 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000908s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000255s ]
---------------------------------------------------------------

[2025-08-04T02:46:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000846s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000693s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246806 LIMIT 100 [ RunTime:0.000315s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000993s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000242s ]
---------------------------------------------------------------

[2025-08-04T02:47:48+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000963s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246868 LIMIT 100 [ RunTime:0.000237s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000972s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000257s ]
---------------------------------------------------------------

[2025-08-04T02:48:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002040s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246929 LIMIT 100 [ RunTime:0.000195s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000835s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000182s ]
---------------------------------------------------------------

[2025-08-04T02:49:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001104s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754246991 LIMIT 100 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000855s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000728s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000366s ]
---------------------------------------------------------------

[2025-08-04T02:50:45+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000842s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000974s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000242s ]
---------------------------------------------------------------

[2025-08-04T02:50:45+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010523s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001071s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000851s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000648s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247045 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000637s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000599s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000894s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000627s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000800s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000672s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000446s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000504s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000362s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000274s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000807s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000502s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000291s ]
---------------------------------------------------------------

[2025-08-04T02:50:45+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.014688s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000667s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000353s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000514s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000608s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000895s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000504s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000711s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000485s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000542s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000519s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000632s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000467s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000304s ]
---------------------------------------------------------------

[2025-08-04T02:50:45+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000930s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000850s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000628s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247045 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000743s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001099s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000743s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000995s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000396s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000492s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000362s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000511s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000766s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000387s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000760s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000275s ]
---------------------------------------------------------------

[2025-08-04T02:50:45+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000991s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000998s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000586s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= 1754236800  AND `add_time` <= 1754323199 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000983s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= 1754236800 )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000383s ]
---------------------------------------------------------------

[2025-08-04T02:50:47+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000965s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000784s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000515s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247046 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000415s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000595s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000302s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000363s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000427s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.001373s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000280s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000357s ]
---------------------------------------------------------------

[2025-08-04T02:50:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000894s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000604s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247052 LIMIT 100 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000891s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000267s ]
---------------------------------------------------------------

[2025-08-04T02:51:53+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001091s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000641s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247113 LIMIT 100 [ RunTime:0.000448s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001012s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000295s ]
---------------------------------------------------------------

[2025-08-04T02:52:03+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000989s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000982s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000239s ]
---------------------------------------------------------------

[2025-08-04T02:52:03+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.010282s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000693s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000327s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000872s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000835s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000419s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000798s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000737s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001001s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000624s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000591s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000283s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000317s ]
---------------------------------------------------------------

[2025-08-04T02:52:03+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001205s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000872s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000625s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247123 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000353s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000546s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000734s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001214s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000393s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000502s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000439s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000568s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000284s ]
---------------------------------------------------------------

[2025-08-04T02:52:04+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000880s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000813s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000422s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000542s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247124 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000360s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000499s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000573s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000237s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000373s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000844s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000587s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000412s ]
---------------------------------------------------------------

[2025-08-04T02:52:04+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001035s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000694s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000526s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000609s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247124 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000393s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000647s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000247s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000208s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000495s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000215s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000187s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.001007s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000322s ]
---------------------------------------------------------------

[2025-08-04T02:52:22+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001081s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000790s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000918s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247142 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000625s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000582s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000387s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000469s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000394s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001065s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000656s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000231s ]
---------------------------------------------------------------

[2025-08-04T02:52:22+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010126s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000610s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000544s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247142 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000354s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000496s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000538s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000691s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000476s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000363s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000326s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000657s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000386s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001183s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000242s ]
---------------------------------------------------------------

[2025-08-04T02:52:22+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000917s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000690s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000254s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001497s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000303s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000929s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000673s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000805s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000560s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000767s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000604s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000794s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000283s ]
---------------------------------------------------------------

[2025-08-04T02:52:22+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001004s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000980s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000302s ]
---------------------------------------------------------------

[2025-08-04T02:52:22+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001028s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000671s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000503s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247142 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000572s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000672s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000196s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000351s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.002075s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000213s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000433s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000223s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000265s ]
---------------------------------------------------------------

[2025-08-04T02:52:41+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001068s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000841s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000671s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247161 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000571s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000752s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000878s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000411s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000589s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000844s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000417s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000202s ]
---------------------------------------------------------------

[2025-08-04T02:52:41+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010468s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000726s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000991s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247161 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000527s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000486s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001340s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000708s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000737s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000274s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000564s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000203s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000448s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000624s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000268s ]
---------------------------------------------------------------

[2025-08-04T02:52:41+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000958s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000464s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000291s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000773s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000469s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000709s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000635s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001095s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000697s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000749s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000502s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000769s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000186s ]
---------------------------------------------------------------

[2025-08-04T02:52:41+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000880s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000792s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000430s ]
---------------------------------------------------------------

[2025-08-04T02:52:41+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001090s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000605s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000471s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247161 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000212s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000764s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000277s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000509s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000430s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000283s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000428s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.001111s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000332s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000327s ]
---------------------------------------------------------------

[2025-08-04T02:52:54+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000883s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000513s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247174 LIMIT 100 [ RunTime:0.000362s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000880s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000470s ]
---------------------------------------------------------------

[2025-08-04T02:52:58+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.015919s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000784s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000244s ]
---------------------------------------------------------------

[2025-08-04T02:52:58+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000940s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.002013s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000670s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247178 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000238s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000399s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000698s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000665s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000302s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000273s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000568s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000668s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000273s ]
---------------------------------------------------------------

[2025-08-04T02:52:58+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.010714s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000428s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000324s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000648s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000349s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000884s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000741s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000468s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000620s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000274s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000224s ]
---------------------------------------------------------------

[2025-08-04T02:52:58+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001040s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000633s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000507s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247178 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000461s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000680s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000363s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000427s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000315s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000247s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000465s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000634s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000401s ]
---------------------------------------------------------------

[2025-08-04T02:52:59+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000874s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000613s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000454s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247179 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000329s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000591s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000526s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000698s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000353s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000227s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000157s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000775s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000426s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000344s ]
---------------------------------------------------------------

[2025-08-04T02:53:13+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.010877s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001179s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000234s ]
---------------------------------------------------------------

[2025-08-04T02:53:13+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001067s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000682s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000275s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000677s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247193 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000391s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000757s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000701s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000432s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000379s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000393s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000446s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000471s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000199s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000370s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000574s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000216s ]
---------------------------------------------------------------

[2025-08-04T02:53:13+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.007761s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000307s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000711s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000481s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000370s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000761s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000732s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000479s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000917s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000288s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000311s ]
---------------------------------------------------------------

[2025-08-04T02:53:13+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001002s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000760s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000652s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247193 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000706s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000787s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000325s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000483s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000391s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000387s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000793s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000428s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000845s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000384s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000264s ]
---------------------------------------------------------------

[2025-08-04T02:53:13+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.001558s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001434s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000515s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247193 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000254s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000781s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000388s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000474s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000391s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000449s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000225s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000222s ]
---------------------------------------------------------------

[2025-08-04T02:53:27+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.012433s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000727s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000285s ]
---------------------------------------------------------------

[2025-08-04T02:53:28+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.003086s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000774s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000500s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247207 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000367s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000624s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000820s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000383s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000370s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000604s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000557s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000288s ]
---------------------------------------------------------------

[2025-08-04T02:53:28+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000965s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000478s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000298s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000805s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000912s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000328s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001020s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000513s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000937s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000942s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000882s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000184s ]
---------------------------------------------------------------

[2025-08-04T02:53:28+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000913s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000688s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000543s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247208 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000441s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000592s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000413s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000693s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000478s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000435s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000798s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000588s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000353s ]
---------------------------------------------------------------

[2025-08-04T02:53:28+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000908s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000913s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000695s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247208 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000272s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000716s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000273s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000386s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000578s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000557s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000459s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000323s ]
---------------------------------------------------------------

[2025-08-04T02:53:46+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010691s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000619s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000511s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247226 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000325s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000530s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000521s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000711s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000386s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000706s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000820s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000282s ]
---------------------------------------------------------------

[2025-08-04T02:53:46+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.013399s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000787s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000750s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247226 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000604s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000727s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000547s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000405s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000320s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000276s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000733s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000590s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000332s ]
---------------------------------------------------------------

[2025-08-04T02:53:46+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000870s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000646s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000277s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000613s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000411s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000704s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000387s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000902s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000438s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000474s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000640s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000197s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000413s ]
---------------------------------------------------------------

[2025-08-04T02:53:46+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000931s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001004s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000313s ]
---------------------------------------------------------------

[2025-08-04T02:53:46+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000868s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000687s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000391s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000579s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247226 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000287s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000699s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000299s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000441s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000880s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000612s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000661s ]
---------------------------------------------------------------

[2025-08-04T02:53:54+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000922s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000533s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247234 LIMIT 100 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000891s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000231s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000219s ]
---------------------------------------------------------------

[2025-08-04T02:54:02+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.010359s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000777s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000250s ]
---------------------------------------------------------------

[2025-08-04T02:54:02+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001238s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000699s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000383s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000840s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000763s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000384s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000855s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000583s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000675s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000352s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000810s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000537s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000279s ]
---------------------------------------------------------------

[2025-08-04T02:54:02+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000893s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001004s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000405s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001007s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247242 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000525s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000736s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000477s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000577s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000540s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000408s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000410s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000500s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000667s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000611s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000353s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001032s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000303s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000251s ]
---------------------------------------------------------------

[2025-08-04T02:54:03+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000980s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000690s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000463s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000634s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247242 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000428s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000518s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000771s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000527s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000412s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000426s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001013s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000359s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000235s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.002054s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000399s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000339s ]
---------------------------------------------------------------

[2025-08-04T02:54:03+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000952s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001028s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000608s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247243 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000297s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000739s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000340s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000517s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000370s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000276s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000776s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000274s ]
---------------------------------------------------------------

[2025-08-04T02:54:19+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000864s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000683s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000660s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247259 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000266s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000482s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000585s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000495s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000827s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000421s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000500s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000660s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000740s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000316s ]
---------------------------------------------------------------

[2025-08-04T02:54:19+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010582s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000841s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000538s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247259 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000344s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000543s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000815s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000483s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000427s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000770s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000363s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000685s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000350s ]
---------------------------------------------------------------

[2025-08-04T02:54:19+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000867s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000502s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000887s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000555s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000736s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000426s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000817s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000478s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000675s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000633s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000700s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000324s ]
---------------------------------------------------------------

[2025-08-04T02:54:20+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000918s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001028s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000255s ]
---------------------------------------------------------------

[2025-08-04T02:54:20+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000876s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000639s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000517s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247260 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000396s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000599s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000326s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000339s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000531s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000462s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000547s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000673s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000237s ]
---------------------------------------------------------------

[2025-08-04T02:54:35+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000987s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000710s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000691s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247275 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000433s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000636s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000372s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000282s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000593s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000706s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000452s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000536s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000285s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000622s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000227s ]
---------------------------------------------------------------

[2025-08-04T02:54:35+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010824s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000937s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000600s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247275 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000624s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000581s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000997s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000426s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000519s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000618s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000482s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000423s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000257s ]
---------------------------------------------------------------

[2025-08-04T02:54:35+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000869s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000580s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000297s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000606s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000468s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000804s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000445s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000799s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000795s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000598s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000736s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000364s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000269s ]
---------------------------------------------------------------

[2025-08-04T02:54:35+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.007232s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000983s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000239s ]
---------------------------------------------------------------

[2025-08-04T02:54:35+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000849s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000591s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000404s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000555s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247275 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000396s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.000521s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000311s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000697s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000455s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000574s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000323s ]
---------------------------------------------------------------

[2025-08-04T02:54:50+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010389s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.002203s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000408s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000525s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247290 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000314s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000494s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000821s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000635s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000588s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000580s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000382s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000612s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000327s ]
---------------------------------------------------------------

[2025-08-04T02:54:50+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000908s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000687s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000581s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247290 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000562s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000339s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000683s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000986s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= 1754236800  AND `time` <= 1754323199 [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= 1754236800  AND `trade_time` <= 1754323199  AND `state` = 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000589s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000268s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000251s ]
---------------------------------------------------------------

[2025-08-04T02:54:50+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000822s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000699s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000397s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000572s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000579s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000372s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001632s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000772s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001048s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000812s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000646s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000257s ]
---------------------------------------------------------------

[2025-08-04T02:54:50+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000907s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000709s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000188s ]
---------------------------------------------------------------

[2025-08-04T02:54:50+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000789s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000625s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000542s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754247290 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-04\",\"2025-08-04\",\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000256s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.001084s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000447s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000546s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000198s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN 1754236800 AND 1754323199 GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN 1754236800 AND 1754323199 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000288s ]
---------------------------------------------------------------

[2025-08-04T02:54:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000937s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000550s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247295 LIMIT 100 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000957s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000255s ]
---------------------------------------------------------------

[2025-08-04T02:55:56+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001159s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000687s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247356 LIMIT 100 [ RunTime:0.000522s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000967s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.001143s ]
---------------------------------------------------------------

[2025-08-04T02:56:58+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001066s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000541s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247417 LIMIT 100 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000850s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000441s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000326s ]
---------------------------------------------------------------

[2025-08-04T02:58:00+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001020s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000628s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247480 LIMIT 100 [ RunTime:0.000256s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000819s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000219s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000232s ]
---------------------------------------------------------------

[2025-08-04T02:59:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000990s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247542 LIMIT 100 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000840s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000212s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000298s ]
---------------------------------------------------------------

[2025-08-04T03:00:03+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001182s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000552s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247603 LIMIT 100 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000854s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000256s ]
---------------------------------------------------------------

[2025-08-04T03:01:05+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000856s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000692s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247665 LIMIT 100 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000842s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000271s ]
---------------------------------------------------------------

[2025-08-04T03:02:06+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000949s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000643s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247726 LIMIT 100 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000911s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000243s ]
---------------------------------------------------------------

[2025-08-04T03:03:07+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000870s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000539s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247787 LIMIT 100 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000819s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000334s ]
---------------------------------------------------------------

[2025-08-04T03:04:08+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000912s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000594s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247848 LIMIT 100 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000928s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000246s ]
---------------------------------------------------------------

[2025-08-04T03:05:10+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001025s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000857s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247910 LIMIT 100 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000793s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000255s ]
---------------------------------------------------------------

[2025-08-04T03:06:13+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000897s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000564s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754247972 LIMIT 100 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000925s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000238s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000219s ]
---------------------------------------------------------------

[2025-08-04T03:07:14+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001009s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000616s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248033 LIMIT 100 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000879s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000225s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000354s ]
---------------------------------------------------------------

[2025-08-04T03:08:15+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001053s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248095 LIMIT 100 [ RunTime:0.000637s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000906s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000207s ]
---------------------------------------------------------------

[2025-08-04T03:09:16+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001096s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000628s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248156 LIMIT 100 [ RunTime:0.000275s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000939s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000205s ]
---------------------------------------------------------------

[2025-08-04T03:10:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000861s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000491s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248218 LIMIT 100 [ RunTime:0.000238s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000934s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000411s ]
---------------------------------------------------------------

[2025-08-04T03:11:19+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001089s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000830s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248279 LIMIT 100 [ RunTime:0.000268s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000928s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000441s ]
---------------------------------------------------------------

[2025-08-04T03:12:22+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000943s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000598s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248341 LIMIT 100 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001033s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000228s ]
---------------------------------------------------------------

[2025-08-04T03:13:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000820s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000553s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248402 LIMIT 100 [ RunTime:0.000220s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000790s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000472s ]
---------------------------------------------------------------

[2025-08-04T03:14:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001421s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000944s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248463 LIMIT 100 [ RunTime:0.000420s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001199s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000479s ]
---------------------------------------------------------------

[2025-08-04T03:15:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000973s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000535s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248524 LIMIT 100 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001069s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000496s ]
---------------------------------------------------------------

[2025-08-04T03:16:25+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001139s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000745s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248585 LIMIT 100 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000868s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000257s ]
---------------------------------------------------------------

[2025-08-04T03:17:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000970s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000546s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248647 LIMIT 100 [ RunTime:0.000209s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000870s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000220s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000591s ]
---------------------------------------------------------------

[2025-08-04T03:18:29+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001061s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754248708 LIMIT 100 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000888s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000195s ]
