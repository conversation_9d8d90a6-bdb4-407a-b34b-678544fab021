---------------------------------------------------------------

[2025-08-04T03:33:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001497s ] mysql:host=mysql;dbname=di<PERSON><PERSON><PERSON>;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001790s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249626 LIMIT 100 [ RunTime:0.000773s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000897s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000342s ]
---------------------------------------------------------------

[2025-08-04T03:34:12+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001111s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001386s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000544s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000735s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000977s ]
[ error ] [8]未定义变量: userLevel
---------------------------------------------------------------

[2025-08-04T03:34:14+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001066s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000864s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.002496s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000277s ]
[ error ] [8]未定义变量: userLevel
---------------------------------------------------------------

[2025-08-04T03:34:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000875s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000530s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249687 LIMIT 100 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001091s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000225s ]
---------------------------------------------------------------

[2025-08-04T03:34:57+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001174s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000471s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000543s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000291s ]
---------------------------------------------------------------

[2025-08-04T03:34:57+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001017s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000494s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000839s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000443s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 0,10 [ RunTime:0.003376s ]
---------------------------------------------------------------

[2025-08-04T03:34:57+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001214s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001190s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000461s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000956s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000407s ]
---------------------------------------------------------------

[2025-08-04T03:35:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001586s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001237s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249747 LIMIT 100 [ RunTime:0.000615s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001054s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000267s ]
---------------------------------------------------------------

[2025-08-04T03:35:48+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001183s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000886s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000584s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.001053s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000559s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 10,10 [ RunTime:0.003821s ]
---------------------------------------------------------------

[2025-08-04T03:35:51+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000884s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000579s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000730s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000391s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 20,10 [ RunTime:0.002934s ]
---------------------------------------------------------------

[2025-08-04T03:35:52+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000796s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000502s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000369s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 30,10 [ RunTime:0.003061s ]
---------------------------------------------------------------

[2025-08-04T03:35:54+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001090s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000689s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000354s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 40,10 [ RunTime:0.002786s ]
---------------------------------------------------------------

[2025-08-04T03:35:55+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000988s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000453s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000526s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 50,10 [ RunTime:0.002902s ]
---------------------------------------------------------------

[2025-08-04T03:35:57+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000885s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000482s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000255s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 60,10 [ RunTime:0.003079s ]
---------------------------------------------------------------

[2025-08-04T03:35:59+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000886s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.001037s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000673s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000367s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 70,10 [ RunTime:0.002860s ]
---------------------------------------------------------------

[2025-08-04T03:36:00+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000988s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000648s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000466s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000364s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 80,10 [ RunTime:0.003012s ]
---------------------------------------------------------------

[2025-08-04T03:36:02+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000872s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000627s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 90,10 [ RunTime:0.004490s ]
---------------------------------------------------------------

[2025-08-04T03:36:03+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000774s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000453s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000586s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 100,10 [ RunTime:0.002778s ]
---------------------------------------------------------------

[2025-08-04T03:36:05+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000956s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000439s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000614s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000324s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 110,10 [ RunTime:0.002780s ]
---------------------------------------------------------------

[2025-08-04T03:36:07+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001450s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000680s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000488s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000626s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000299s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 120,10 [ RunTime:0.002999s ]
---------------------------------------------------------------

[2025-08-04T03:36:10+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000830s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000462s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000386s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000620s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 130,10 [ RunTime:0.003050s ]
---------------------------------------------------------------

[2025-08-04T03:36:11+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001004s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000619s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000466s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 140,10 [ RunTime:0.003189s ]
---------------------------------------------------------------

[2025-08-04T03:36:13+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000933s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000646s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 150,10 [ RunTime:0.002839s ]
---------------------------------------------------------------

[2025-08-04T03:36:14+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000938s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000546s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 160,10 [ RunTime:0.002815s ]
---------------------------------------------------------------

[2025-08-04T03:36:15+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001046s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000525s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000591s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000394s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 170,10 [ RunTime:0.002913s ]
---------------------------------------------------------------

[2025-08-04T03:36:16+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000907s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.012841s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000451s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000525s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 180,10 [ RunTime:0.004967s ]
---------------------------------------------------------------

[2025-08-04T03:36:17+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.002899s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000513s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000430s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000659s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 190,10 [ RunTime:0.003032s ]
---------------------------------------------------------------

[2025-08-04T03:36:18+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001280s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000807s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000282s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 200,10 [ RunTime:0.002912s ]
---------------------------------------------------------------

[2025-08-04T03:36:19+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001087s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000520s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000438s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000395s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 210,10 [ RunTime:0.003035s ]
---------------------------------------------------------------

[2025-08-04T03:36:20+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.002814s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000515s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000791s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000272s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 220,10 [ RunTime:0.003097s ]
---------------------------------------------------------------

[2025-08-04T03:36:21+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001157s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000536s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 230,10 [ RunTime:0.003422s ]
---------------------------------------------------------------

[2025-08-04T03:36:23+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000989s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000539s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000359s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 950,10 [ RunTime:0.004222s ]
---------------------------------------------------------------

[2025-08-04T03:36:26+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000947s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000635s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 940,10 [ RunTime:0.003841s ]
---------------------------------------------------------------

[2025-08-04T03:36:29+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001053s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000589s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000408s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000770s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 930,10 [ RunTime:0.003809s ]
---------------------------------------------------------------

[2025-08-04T03:36:30+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000941s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000627s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000675s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 920,10 [ RunTime:0.003727s ]
---------------------------------------------------------------

[2025-08-04T03:36:31+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000869s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000708s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000570s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000701s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 910,10 [ RunTime:0.003539s ]
---------------------------------------------------------------

[2025-08-04T03:36:33+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001191s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000852s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000440s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000828s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000434s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 900,10 [ RunTime:0.003611s ]
---------------------------------------------------------------

[2025-08-04T03:36:34+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000987s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 890,10 [ RunTime:0.003509s ]
---------------------------------------------------------------

[2025-08-04T03:36:36+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001379s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000923s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000405s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000792s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000514s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 880,10 [ RunTime:0.004543s ]
---------------------------------------------------------------

[2025-08-04T03:36:40+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000906s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000596s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000418s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 870,10 [ RunTime:0.003465s ]
---------------------------------------------------------------

[2025-08-04T03:36:40+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000833s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000466s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000534s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000570s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 860,10 [ RunTime:0.005455s ]
---------------------------------------------------------------

[2025-08-04T03:36:41+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001498s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001647s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000547s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 850,10 [ RunTime:0.004490s ]
---------------------------------------------------------------

[2025-08-04T03:36:43+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001110s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000496s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000373s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000522s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000271s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 840,10 [ RunTime:0.003231s ]
---------------------------------------------------------------

[2025-08-04T03:36:44+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000979s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 830,10 [ RunTime:0.003528s ]
---------------------------------------------------------------

[2025-08-04T03:36:45+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.002582s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000861s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.001106s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000503s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 820,10 [ RunTime:0.003500s ]
---------------------------------------------------------------

[2025-08-04T03:36:46+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000870s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001260s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.001448s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000497s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000302s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `add_time` DESC LIMIT 810,10 [ RunTime:0.003528s ]
---------------------------------------------------------------

[2025-08-04T03:36:48+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000874s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000724s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249808 LIMIT 100 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000996s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000407s ]
