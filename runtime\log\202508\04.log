---------------------------------------------------------------

[2025-08-04T03:33:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001497s ] mysql:host=mysql;dbname=di<PERSON><PERSON><PERSON>;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001790s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249626 LIMIT 100 [ RunTime:0.000773s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000897s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000342s ]
---------------------------------------------------------------

[2025-08-04T03:34:12+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001111s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001386s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000544s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000735s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000977s ]
[ error ] [8]未定义变量: userLevel
---------------------------------------------------------------

[2025-08-04T03:34:14+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001066s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000864s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.002496s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` [ RunTime:0.000277s ]
[ error ] [8]未定义变量: userLevel
---------------------------------------------------------------

[2025-08-04T03:34:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000875s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000530s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754249687 LIMIT 100 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001091s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000225s ]
