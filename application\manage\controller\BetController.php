<?php

namespace app\manage\controller;

use app\manage\controller\Common;
use think\Db;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Exception;

class BetController extends CommonController
{
	/**
	 * 空操作处理
	 */
	public function _empty()
	{
		return $this->lists();
	}

	public function taskTplAdd()
	{
		return view('', []);
	}

	public function taskModelEdit()
	{
		if (request()->isAjax()) return model('TaskTpl')->edit();

		$id                       = input('get.id/d');
		$data                     = model('TaskTpl')->where('id', $id)->find();
		$data['end_time']         = ($data['end_time']) ? date('Y-m-d', $data['end_time']) : '';
		// 安全处理JSON字段，防止数据错位导致的错误
		$data['finish_condition'] = $data['finish_condition'] ? json_decode($data['finish_condition'], true) : [];
		if (!is_array($data['finish_condition'])) $data['finish_condition'] = [];

		$data['examine_demo'] = $data['examine_demo'] ? json_decode($data['examine_demo'], true) : [];
		if (!is_array($data['examine_demo'])) $data['examine_demo'] = [];

		$data['task_step'] = $data['task_step'] ? json_decode($data['task_step'], true) : [];
		if (!is_array($data['task_step'])) $data['task_step'] = [];

		// 处理详情图片字段
		if (isset($data['detail_image'])) {
			$data['detail_image'] = $data['detail_image'] ? json_decode($data['detail_image'], true) : [];
			if (!is_array($data['detail_image'])) $data['detail_image'] = [];
		}

		$taskClass                = model('TaskClass')->order('num', 'asc')->select();
		$taskClass                = $taskClass ? $taskClass->toArray() : [];
		$userLevel                = model('UserGrade')->order('grade', 'asc')->select();
		$userLevel                = $userLevel ? $userLevel->toArray() : [];

		return view('', [
			'data'      => $data,
			'taskClass' => $taskClass,
			'userLevel' => $userLevel
		]);
	}

	public function taskModelDel()
	{
		return model('TaskTpl')->del();
	}

	public function taskModelAdd()
	{
		if (request()->isAjax()) return model('TaskTpl')->add();

		$taskClass = model('TaskClass')->order('num', 'asc')->select();
		$taskClass = $taskClass ? $taskClass->toArray() : [];
		$userLevel = model('UserGrade')->order('grade', 'asc')->select();
		$userLevel = $userLevel ? $userLevel->toArray() : [];

		return view('', [
			'taskClass' => $taskClass,
			'userLevel' => $userLevel
		]);
	}

	public function taskModelList()
	{
		if (request()->isAjax()) {
			$param = input('param.');

			//查询条件初始化
			$where = array();
			// 模板名
			if (isset($param['name']) && $param['name']) {
				$where[] = array(['name', '=', $param['name']]);
			}

			// 标题
			if (isset($param['title']) && $param['title']) {
				$where[] = array('title', 'like', '%' . $param['title'] . '%');
			}



			$count = model('TaskTpl')->join('ly_task_class', 'ly_task_tpl.task_class=ly_task_class.id')->where($where)->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 10; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'add_time';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

			//查询符合条件的数据
			$data = model('TaskTpl')->field('ly_task_tpl.*,ly_task_class.group_name')->join('ly_task_class', 'ly_task_tpl.task_class=ly_task_class.id')->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();


			$t 					= time();
			$end_time			= mktime(0, 0, 0, date("m", $t), date("d", $t), date("Y", $t));

			foreach ($data as $key => &$value) {

				$value['statusStr']       		= config('custom.taskStatus')[$value['status']];
				if ($value['end_time'] < $end_time) {
					$value['revoke']	=	1;
				} else {
					$value['revoke']	=	0;
				}
				$value['task_type_str']   		= config('custom.taskType')[$value['task_type']];
				$value['speed']           		= $value['receive_number'] . '/' . $value['total_number'];
				if ($value['task_pump']) {
					$value['speed_total_price']     = $value['total_price'] . '+' . $value['task_pump'];
				} else {
					$value['speed_total_price']     = $value['total_price'];
				}
				if ($value['username']) {
					$value['username']     = $value['username'];
				} else {
					$value['username']     = '管理员';
				}
				$value['format_end_time'] 		= ($value['end_time']) ? date('Y-m-d', $value['end_time']) : '';
				$value['format_add_time'] 		= ($value['add_time']) ? date('Y-m-d H:i:s', $value['add_time']) : '';
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}
		return view();
	}

	/**
	 * 用户资金流水
	 */
	public function financial()
	{
		if (request()->isAjax()) {
			$param = input('post.');

			//查询条件组装
			$where = array();
			$where[] = array('types', '=', 1);

			if (isset($param['isUser'])) {
				$where[] = array('types', '=', $param['isUser']);
				$pageParam['isUser'] = $param['isUser'];
			}
			//搜索类型
			if (isset($param['search_type']) && $param['search_type'] && isset($param['search_content']) && $param['search_content']) {
				switch ($param['search_type']) {
					case 'remarks':
						$where[] = array('remarks', 'like', '%' . $param['search_content'] . '%');
						break;
					default:
						$where[] = array($param['search_type'], '=', $param['search_content']);
						break;
				}
			}
			//交易类型
			if (isset($param['trade_type']) && $param['trade_type']) {
				$where[] = array('trade_type', '=', $param['trade_type']);
			}
			//交易金额
			if (isset($param['price1']) && $param['price1']) {
				$where[] = array('trade_amount', '>=', $param['price1']);
			}
			//交易金额
			if (isset($param['price2']) && $param['price2']) {
				$where[] = array('trade_amount', '<=', $param['price2']);
			}
			//时间
			if (isset($param['datetime_range']) && $param['datetime_range']) {
				$dateTime = explode(' - ', $param['datetime_range']);
				$where[] = array('trade_time', '>=', strtotime($dateTime[0]));
				$where[] = array('trade_time', '<=', strtotime($dateTime[1]));
			} else {
				$todayStart = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
				$where[] = array('trade_time', '>=', $todayStart);
				$todayEnd = mktime(23, 59, 59, date('m'), date('d'), date('Y'));
				$where[] = array('trade_time', '<=', $todayEnd);
			}

			$count              = model('TradeDetails')->where($where)->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'trade_time';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

			//查询符合条件的数据，关联用户表获取来源用户信息
			$data = model('TradeDetails')->alias('td')
				->leftJoin('users u', 'td.source_uid = u.id')
				->field('td.*, u.username as source_username')
				->where($where)
				->order('td.' . $param['sortField'], $param['sortType'])
				->limit($limitOffset, $param['limit'])
				->select()->toArray();
			//部分元素重新赋值
			$tradeType   = config('custom.transactionType'); //交易类型
			$orderColor  = config('manage.color');
			$adminColor  = config('manage.adminColor');
			foreach ($data as $key => &$value) {
				$value['trade_time']     = date('Y-m-d H:i:s', $value['trade_time']);
				$value['tradeType']      = $tradeType[$value['trade_type']];
				$value['tradeTypeColor'] = $adminColor[$value['trade_type']];
				$value['statusStr']      = config('custom.tradedetailsStatus')[$value['state']];
				$value['statusColor']    = $orderColor[$value['state']];
				$value['front_type_str'] = config('custom.front_type')[$value['front_type']];
				$value['payway_str']     = config('custom.payway')[$value['payway']];
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}

		return view();
	}
	/**
	 * 商户资金流水
	 */
	public function mfinancial()
	{
		if (request()->isAjax()) {
			$param = input('post.');

			//查询条件组装
			$where = array();
			$where[] = array('types', '=', 2);

			if (isset($param['isUser'])) {
				$where[] = array('types', '=', $param['isUser']);
				$pageParam['isUser'] = $param['isUser'];
			}
			//搜索类型
			if (isset($param['search_type']) && $param['search_type'] && isset($param['search_content']) && $param['search_content']) {
				switch ($param['search_type']) {
					case 'remarks':
						$where[] = array('remarks', 'like', '%' . trim($param['search_content']) . '%');
						break;
					default:
						$where[] = array($param['search_type'], '=', trim($param['search_content']));
						break;
				}
			}
			//交易类型
			if (isset($param['trade_type']) && $param['trade_type']) {
				$where[] = array('trade_type', '=', $param['trade_type']);
			}
			//交易金额
			if (isset($param['price1']) && $param['price1']) {
				$where[] = array('trade_amount', '>=', $param['price1']);
			}
			//交易金额
			if (isset($param['price2']) && $param['price2']) {
				$where[] = array('trade_amount', '<=', $param['price2']);
			}
			//时间
			if (isset($param['datetime_range']) && $param['datetime_range']) {
				$dateTime = explode(' - ', $param['datetime_range']);
				$where[] = array('trade_time', '>=', strtotime($dateTime[0]));
				$where[] = array('trade_time', '<=', strtotime($dateTime[1]));
			} else {
				$todayStart = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
				$where[] = array('trade_time', '>=', $todayStart);
				$todayEnd = mktime(23, 59, 59, date('m'), date('d'), date('Y'));
				$where[] = array('trade_time', '<=', $todayEnd);
			}

			$count              = model('TradeDetails')->where($where)->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'trade_time';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

			//查询符合条件的数据
			$data = model('TradeDetails')->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();
			//部分元素重新赋值
			$tradeType   = config('custom.transactionType'); //交易类型
			$orderColor  = config('manage.color');
			$adminColor  = config('manage.adminColor');
			foreach ($data as $key => &$value) {
				$value['trade_time']      = date('Y-m-d H:i:s', $tradeType[$value['trade_time']]);
				$value['tradeType']      = $tradeType[$value['trade_type']];
				$value['tradeTypeColor'] = $adminColor[$value['trade_type']];
				$value['statusStr']      = config('custom.tradedetailsStatus')[$value['state']];
				$value['statusColor']    = $orderColor[$value['state']];
				$value['front_type_str'] = config('custom.front_type')[$value['front_type']];
				$value['payway_str']     = config('custom.payway')[$value['payway']];
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}

		return view('financial');
	}
	/**
	 * 流水详情
	 */
	public function financial_dateils()
	{
		$data = model('TradeDetails')->financialDateils();

		$this->assign('info', $data);

		return $this->fetch();
	}
	/**
	 * 交易列表
	 * @return [type] [description]
	 */
	public function buytrans()
	{
		$data = model('UserTransaction')->transAction($bitype = 1); //买币
		return view('trans_action', [
			'data' => $data
		]);
	}

	public function selltrans()
	{
		$data = model('UserTransaction')->transAction($bitype = 2); //卖币
		return view('trans_action', [
			'data' => $data
		]);
	}

	//交易详情
	public function transdateils()
	{

		$data = model('UserTransaction')->transdateils(); //卖币

		$this->assign('orderInfo', $data);

		return $this->fetch();
	}

	//交易订单操作
	public function operationtrans()
	{
		return model('UserTransaction')->operationtrans();
	}

	/**
	 * 回调
	 */
	public function callBack()
	{
		$id = input('post.id/d');
		$order = model('Order')->where('id', $id)->findOrEmpty();
		if (!$order) return '订单不存在';

		$callBackData = array(
			'uid'              => $order['uid'],
			'merchantId'       => model('Merchant')->where('id', $order['mid'])->value('merchantid'),
			'timestamp'        => $order['timestamp'],
			'signatureMethod'  => 'HmacSHA256',
			'signatureVersion' => 1,
			'orderId'          => $order['orderid'],
			'status'           => 3,
			'jOrderId'         => $order['jorderid'],
			'notifyUrl'        => base64_decode($order['notifyurl']),
			'orderType'        => $order['ordertype'],
			'amount'           => $order['oamount'],
			'currency'         => $order['currency'],
			'actualAmount'     => $order['oactualamount'],
			'fee'              => $order['feeamount'],
			'payWay'           => $order['payway'],
			'payTime'          => $order['paytimes'],
			'jExtra'           => base64_decode($order['jextra']),
			'mkey'             => $order['mkey'],
		);
		model('api/Order')->Callback($callBackData);

		return 1;
	}

	/**
	 * 添加项目
	 * @return [type] [description]
	 */
	public function taskAdd()
	{
		if (request()->isAjax()) return model('Task')->add();

		$taskClass = model('TaskClass')->order('num', 'asc')->select();
		$taskClass = $taskClass ? $taskClass->toArray() : [];
		$userLevel = model('UserGrade')->order('grade+0', 'asc')->select();
		$userLevel = $userLevel ? $userLevel->toArray() : [];

		return view('', [
			'taskClass' => $taskClass,
			'userLevel' => $userLevel
		]);
	}

	/**
	 * 编辑项目
	 * @return [type] [description]
	 */
	public function taskEdit()
	{
		if (request()->isAjax()) return model('Task')->edit();

		$id                       = input('get.id/d');
		$data                     = model('Task')->where('id', $id)->find();
		$tplid                    = input('get.tplid/d');
		if ($tplid) {
			$data                 = model('TaskTpl')->where('id', $tplid)->find();
			unset($data['id']);
		}


		$data['end_time']         = ($data['end_time']) ? date('Y-m-d', $data['end_time']) : '';
		// 安全处理JSON字段，防止数据错位导致的错误
		$data['finish_condition'] = $data['finish_condition'] ? json_decode($data['finish_condition'], true) : [];
		if (!is_array($data['finish_condition'])) $data['finish_condition'] = [];

		$data['examine_demo'] = $data['examine_demo'] ? json_decode($data['examine_demo'], true) : [];
		if (!is_array($data['examine_demo'])) $data['examine_demo'] = [];

		$data['task_step'] = $data['task_step'] ? json_decode($data['task_step'], true) : [];
		if (!is_array($data['task_step'])) $data['task_step'] = [];

		// 处理详情图片字段
		if (isset($data['detail_image'])) {
			$data['detail_image'] = $data['detail_image'] ? json_decode($data['detail_image'], true) : [];
			if (!is_array($data['detail_image'])) $data['detail_image'] = [];
		}

		$taskClass                = model('TaskClass')->order('num', 'asc')->select();
		$taskClass                = $taskClass ? $taskClass->toArray() : [];
		$userLevel                = model('UserGrade')->order('grade', 'asc')->select();
		$userLevel                = $userLevel ? $userLevel->toArray() : [];

		return view('', [
			'data'      => $data,
			'taskClass' => $taskClass,
			'userLevel' => $userLevel,
			'tplid' => $tplid
		]);
	}

	/**
	 * 删除项目
	 * @return [type] [description]
	 */
	public function taskDel()
	{
		return model('Task')->del();
	}

	public function projectRecommend()
	{
		if (!request()->isAjax()) return '非法提交';
		$param = input('post.'); //获取参数
		if (!$param) return '提交失败';
		//更新
		$updateRes = model('Project')->where('id', $param['id'])->setField('recommend', $param['val']);
		if (!$updateRes) return '修改失败';
		//添加操作日志
		$actionStr = $param['val'] == 2 ? '非' : '';
		$title = model('Project')->where('id', $param['id'])->value('title');
		model('Actionlog')->actionLog(session('manage_username'), '将项目' . $title . '设为' . $actionStr . '推荐', 1);

		return 1;
	}

	/**
	 * 项目类型
	 * @return [type] [description]
	 */
	public function taskclass()
	{
		if (request()->isAjax()) {
			$param = input('post.');

			$count              = model('TaskClass')->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'id';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

			//查询符合条件的数据
			$data = model('TaskClass')->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();
			foreach ($data as $key => &$value) {
				$value['stateStr'] = ($value['state'] == 1) ? '开启' : '关闭';
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}

		return view('task_class');
	}

	/**
	 * 添加类型
	 * @return [type] [description]
	 */
	public function TaskClassAdd()
	{
		if (request()->isAjax()) return model('TaskClass')->TaskClassAdd();

		return view();
	}

	/**
	 * 编辑类型
	 * @return [type] [description]
	 */
	public function TaskClassEdit()
	{
		if (request()->isAjax()) return model('TaskClass')->TaskClassEdit();

		$id = input('get.id/d');
		$data = model('TaskClass')->where('id', $id)->find();

		return view('', [
			'data'        => $data
		]);
	}

	/**
	 * 删除类型
	 * @return [type] [description]
	 */
	public function TaskClassDel()
	{
		return model('TaskClass')->TaskClassDel();
	}

	/**
	 * 返还方式
	 * @return [type] [description]
	 */
	public function returnType()
	{
		if (request()->isAjax()) {
			$param = input('post.');

			$count              = model('RepaymentMethod')->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'id';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

			//查询符合条件的数据
			$data = model('RepaymentMethod')->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();
			foreach ($data as $key => &$value) {
				$value['stateStr'] = ($value['state'] == 1) ? '开启' : '关闭';
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}

		return view();
	}

	/**
	 * 添加方式
	 * @return [type] [description]
	 */
	public function returnTypeAdd()
	{
		if (request()->isAjax()) return model('RepaymentMethod')->projectTypeAdd();

		return view();
	}

	/**
	 * 编辑类型
	 * @return [type] [description]
	 */
	public function returnTypeEdit()
	{
		if (request()->isAjax()) return model('RepaymentMethod')->projectTypeEdit();

		$id = input('get.id/d');
		$data = model('RepaymentMethod')->where('id', $id)->find();

		return view('', [
			'data'        => $data
		]);
	}

	/**
	 * 删除方式
	 * @return [type] [description]
	 */
	public function returnTypeDel()
	{
		return model('RepaymentMethod')->projectTypeDel();
	}

	/**
	 * 投资记录
	 * @return [type] [description]
	 */
	public function investList()
	{
		if (request()->isAjax()) {
			$param = input('post.');
			//查询条件组装
			$where = array();

			// 用户名
			if (isset($param['username']) && $param['username']) {
				$where[] = array('users.username', 'like', '%' . $param['username'] . '%');
			}
			// 项目
			if (isset($param['project']) && $param['project']) {
				$where[] = array('pid', 'like', '%' . $param['username'] . '%');
			}
			// 推荐
			if (isset($param['state']) && $param['state']) {
				$where[] = array('state', '=', $param['state']);
			}
			// 时间
			if (isset($param['date_range']) && $param['date_range']) {
				$dateTime = explode(' - ', $param['date_range']);
				$where[] = array('ly_order.add_time', '>=', strtotime($dateTime[0]));
				$where[] = array('ly_order.add_time', '<=', strtotime($dateTime[1]));
			}
			// else{
			// 	$todayStart = mktime(0,0,0,date('m'),date('d'),date('Y'));
			// 	$where[] = array('ly_order.add_time','>=',$todayStart);
			// 	$todayEnd = mktime(23,59,59,date('m'),date('d'),date('Y'));
			// 	$where[] = array('ly_order.add_time','<=',$todayEnd);
			// }

			$count              = model('Order')->join('project', 'ly_order.pid=project.id')->join('users', 'ly_order.uid=users.id')->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'add_time';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

			//查询符合条件的数据
			$data = model('Order')
				->field('ly_order.*,project.title,users.username,phone')
				->join('project', 'ly_order.pid=project.id')
				->join('users', 'ly_order.uid=users.id')
				->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();
			foreach ($data as $key => &$value) {
				switch ($value['state']) {
					case '1':
						$value['stateStr'] = '完成';
						break;
					case '2':
						$value['stateStr'] = '取消';
						break;
					default:
						$value['stateStr'] = '进行中';
						break;
				}
				$value['yieldRate']   = $value['daily_income'] . ' % + ' . $value['rebate'] . ' %';
				$value['bearing_day'] = date('Y-m-d H:i:s', $value['bearing_day']);
				$value['due_day']     = date('Y-m-d H:i:s', $value['due_day']);
				$value['add_time']    = date('Y-m-d H:i:s', $value['add_time']);
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}

		return view();
	}

	/**
	 * 记录详情
	 * @return [type] [description]
	 */
	public function investDetails() {}

	/**
	 * 查看合同
	 * @return [type] [description]
	 */
	public function investPact()
	{
		if (request()->isAjax()) return model('Order')->investPact();

		$id = input('get.id/d');
		$data = model('Order')->where('id', $id)->find()->toArray();

		return view('', [
			'data' => $data
		]);
	}

	/**
	 * 任务列表
	 * @return [type] [description]
	 */
	public function taskList()
	{
		if (request()->isAjax()) {
			$param = input('param.');

			//查询条件初始化
			$where = array();
			// 标题
			if (isset($param['username']) && $param['username']) {
				$where[] = array(['username', '=', $param['username']]);
			}

			// 标题
			if (isset($param['title']) && $param['title']) {
				$where[] = array('title', 'like', '%' . $param['title'] . '%');
			}

			// 状态
			if (isset($param['status']) && $param['status']) {
				$where[] = array(['status', '=', $param['status']]);
			}

			// 类型
			if (isset($param['task_type']) && $param['task_type']) {
				$where[] = array('task_type', '=', $param['task_type']);
			}
			// 分类
			if (isset($param['task_class']) && $param['task_class']) {
				$where[] = array('task_class', '=', $param['task_class']);
			}

			// 显示状态
			if (isset($param['is_visible']) && $param['is_visible'] !== '') {
				$where[] = array('is_visible', '=', $param['is_visible']);
			}
			// 时间
			if (isset($param['datetime_range']) && $param['datetime_range']) {
				$dateTime = explode(' - ', $param['datetime_range']);
				$where[] = ['add_time', 'between time', [$dateTime[0], $dateTime[1]]];
			}
			// 移除默认时间限制，允许查看所有历史数据

			$count = model('Task')->join('ly_task_class', 'ly_task.task_class=ly_task_class.id')->where($where)->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 10; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'id';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'asc';

			//查询符合条件的数据
			$data = model('Task')->field('ly_task.*,ly_task_class.group_name')->join('ly_task_class', 'ly_task.task_class=ly_task_class.id')->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();


			$t 					= time();
			$end_time			= mktime(0, 0, 0, date("m", $t), date("d", $t), date("Y", $t));

			foreach ($data as $key => &$value) {

				$value['statusStr']       		= config('custom.taskStatus')[$value['status']];
				if ($value['end_time'] < $end_time) {
					$value['revoke']	=	1;
				} else {
					$value['revoke']	=	0;
				}
				$value['task_type_str']   		= config('custom.taskType')[$value['task_type']];
				$value['speed']           		= $value['receive_number'] . '/' . $value['total_number'];
				if ($value['task_pump']) {
					$value['speed_total_price']     = $value['total_price'] . '+' . $value['task_pump'];
				} else {
					$value['speed_total_price']     = $value['total_price'];
				}
				if ($value['username']) {
					$value['username']     = $value['username'];
				} else {
					$value['username']     = '管理员';
				}
				$value['format_end_time'] 		= ($value['end_time']) ? date('Y-m-d', $value['end_time']) : '';
				$value['format_add_time'] 		= ($value['add_time']) ? date('Y-m-d H:i:s', $value['add_time']) : '';

				// 处理详情图片字段，避免json_decode错误
				if (isset($value['detail_image'])) {
					if (is_array($value['detail_image'])) {
						// 如果已经是数组，重新编码为JSON字符串供前端使用
						$value['detail_image'] = json_encode($value['detail_image'], JSON_UNESCAPED_SLASHES);
					} elseif (is_string($value['detail_image']) && !empty($value['detail_image'])) {
						// 如果是字符串，验证JSON格式
						$decoded = json_decode($value['detail_image'], true);
						if (json_last_error() === JSON_ERROR_NONE) {
							// JSON格式正确，保持原样
							// $value['detail_image'] = $value['detail_image'];
						} else {
							// 不是有效JSON，可能是单个URL
							if (filter_var($value['detail_image'], FILTER_VALIDATE_URL)) {
								$value['detail_image'] = json_encode([$value['detail_image']], JSON_UNESCAPED_SLASHES);
							} else {
								$value['detail_image'] = '';
							}
						}
					} else {
						$value['detail_image'] = '';
					}
				}
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}

		$taskClass = model('TaskClass')->order('num', 'asc')->select()->toArray();

		return view('', [
			'taskClass' => $taskClass
		]);
	}

	/**
	 * 切换任务显示状态
	 */
	public function taskToggleVisible()
	{
		if (!request()->isAjax()) {
			return json(['code' => 1, 'msg' => '非法请求']);
		}

		$param = input('param.');

		if (!isset($param['id']) || !$param['id']) {
			return json(['code' => 1, 'msg' => '参数错误']);
		}

		if (!isset($param['is_visible']) || !in_array($param['is_visible'], ['0', '1'])) {
			return json(['code' => 1, 'msg' => '显示状态参数错误']);
		}

		try {
			$result = model('Task')->where('id', $param['id'])->update([
				'is_visible' => $param['is_visible']
			]);

			if ($result) {
				$statusText = $param['is_visible'] == '1' ? '显示' : '隐藏';
				return json(['code' => 0, 'msg' => '任务已设置为' . $statusText]);
			} else {
				return json(['code' => 1, 'msg' => '操作失败，请重试']);
			}
		} catch (\Exception $e) {
			return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage()]);
		}
	}

	/**
	 * 批量修改任务分佣比例
	 */
	public function batchUpdateRebate()
	{
		if (!request()->isAjax()) {
			return json(['code' => 1, 'msg' => '非法请求']);
		}

		$param = input('param.');

		// 验证参数
		if (!isset($param['task_ids']) || empty($param['task_ids'])) {
			return json(['code' => 1, 'msg' => '请选择要修改的任务']);
		}

		$taskIds = explode(',', $param['task_ids']);
		$taskIds = array_filter($taskIds); // 过滤空值

		if (empty($taskIds)) {
			return json(['code' => 1, 'msg' => '任务ID参数错误']);
		}

		// 构建更新数据
		$updateData = [];

		if (isset($param['task_rebate1']) && $param['task_rebate1'] !== '') {
			$rebate1 = floatval($param['task_rebate1']);
			if ($rebate1 < 0 || $rebate1 > 100) {
				return json(['code' => 1, 'msg' => '一级分佣比例必须在0-100之间']);
			}
			$updateData['task_rebate1'] = $rebate1;
		}

		if (isset($param['task_rebate2']) && $param['task_rebate2'] !== '') {
			$rebate2 = floatval($param['task_rebate2']);
			if ($rebate2 < 0 || $rebate2 > 100) {
				return json(['code' => 1, 'msg' => '二级分佣比例必须在0-100之间']);
			}
			$updateData['task_rebate2'] = $rebate2;
		}

		if (isset($param['task_rebate3']) && $param['task_rebate3'] !== '') {
			$rebate3 = floatval($param['task_rebate3']);
			if ($rebate3 < 0 || $rebate3 > 100) {
				return json(['code' => 1, 'msg' => '三级分佣比例必须在0-100之间']);
			}
			$updateData['task_rebate3'] = $rebate3;
		}

		if (empty($updateData)) {
			return json(['code' => 1, 'msg' => '请至少填写一个分佣比例']);
		}

		try {
			// 批量更新
			$result = model('Task')->where('id', 'in', $taskIds)->update($updateData);

			if ($result) {
				$updateCount = count($taskIds);
				return json(['code' => 0, 'msg' => "成功修改了 {$updateCount} 个任务的分佣比例"]);
			} else {
				return json(['code' => 1, 'msg' => '修改失败，请重试']);
			}
		} catch (\Exception $e) {
			return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage()]);
		}
	}

	/**
	 * 任务审核
	 * @return [type] [description]
	 */
	public function taskAudit()
	{
		if (request()->isAjax()) return model('Task')->audit();

		$id                       = input('get.id/d');

		$data                     = model('Task')->where('id', $id)->find();
		//$data['end_time']         = ($data['end_time']) ? date('Y-m-d', $data['end_time']) : '';
		//$data['finish_condition'] = json_decode($data['finish_condition'], true);
		//$data['task_step']        	= json_decode($data['task_step'], true);

		if ($data['examine_demo']) {
			$data['examine_demo']   = json_decode($data['examine_demo'], true);
		} else {
			$data['examine_demo']	=	array();
		}

		$t 					= time();
		$end_time			= mktime(0, 0, 0, date("m", $t), date("d", $t), date("Y", $t));
		$data['revoke']		= 0; //撤销
		if ($data['end_time'] < $end_time) {
			$data['revoke']	=	1;
		}

		$data['statusStr']			= config('custom.taskStatus')[$data['status']];
		//$taskClass                = model('TaskClass')->select()->toArray();

		return view('', [
			'data'      => $data,
			//'taskClass' => $taskClass
		]);
	}
	/**
	 * 任务记录
	 * @return [type] [description]
	 */
	public function userTaskList()
	{
		if (request()->isAjax()) {

			$param = input('param.');

			//查询条件初始化
			$where = array();

			// 用户名
			if (isset($param['username']) && $param['username']) {
				$where[] = array(['ly_user_task.username', '=', $param['username']]);
			}

			// 状态
			if (isset($param['status']) && $param['status']) {
				$where[] = array(['ly_user_task.status', '=', $param['status']]);
			}

			// 时间
			if (isset($param['datetime_range']) && $param['datetime_range']) {
				$dateTime = explode(' - ', $param['datetime_range']);
				$where[] = ['ly_user_task.add_time', 'between time', [$dateTime[0], $dateTime[1]]];
			} else {
				//$todayStart = mktime(0,0,0,date('m'),date('d'),date('Y'));
				//$todayEnd = mktime(23,59,59,date('m'),date('d'),date('Y'));
				//$where[] = ['ly_user_task.add_time', 'between time', [$todayStart, $todayEnd]];
			}

			$count = model('UserTask')->join('ly_task', 'ly_task.id=ly_user_task.task_id')->where($where)->count(); // 总记录数


			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 10; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			$param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'trial_time';
			$param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

			//查询符合条件的数据
			$data = model('UserTask')->field('ly_task.title,ly_user_task.*')->join('ly_task', 'ly_task.id=ly_user_task.task_id')->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();

			foreach ($data as $key => &$value) {
				$value['statusStr']      = config('custom.cntaskOrderStatus')[$value['status']];
				$value['add_time'] 		 = ($value['add_time']) ? date('Y-m-d H:i:s', $value['add_time']) : ''; //接单时间
				$value['o_id'] 		 = $value['id']; //接单时间
			}

			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}

		return view('');
	}

	/**
	 * 任务记录审核
	 * @return [type] [description]
	 */
	public function userTaskAudit()
	{
		if (request()->isAjax())	return model('Task')->userTaskAudit();

		$id                       	= input('get.id/d');

		$data                     	= model('UserTask')->field('ly_task.content,ly_task.examine_demo,ly_task.title,ly_task.username,ly_task.link_info,ly_user_task.status as o_status,ly_user_task.id,ly_user_task.add_time as o_add_time,ly_user_task.username as o_username,ly_user_task.examine_demo as o_examine_demo,ly_user_task.trial_time,ly_user_task.id as order_id,ly_user_task.uid as o_uid,ly_user_task.username as o_username,ly_user_task.trial_remarks,ly_user_task.handle_remarks,ly_user_task.complete_time as o_complete_time,ly_user_task.handle_time')->join('ly_task', 'ly_task.id=ly_user_task.task_id')->where(array(['ly_user_task.id', '=', $id]))->find();

		if ($data['examine_demo']) {
			$data['examine_demo']   = json_decode($data['examine_demo'], true);
		} else {
			$data['examine_demo']	=	array();
		}

		if ($data['o_examine_demo']) {
			if (strstr($data['o_examine_demo'], '[')) {
				$data['o_examine_demo']   = json_decode($data['o_examine_demo'], true);
			} else {
				$data['o_examine_demo']   = array($data['o_examine_demo']);
			}
		} else {
			$data['o_examine_demo']	=	array();
		}

		$data['statusStr']			= config('custom.cntaskOrderStatus')[$data['o_status']];

		return view('', [
			'data'      => $data,
		]);
	}

	/**
	 * 一键审核
	 * @return [type] [description]
	 */
	public function batchFinish()
	{
		// if (request()->isAjax()) {
		// }
		return json(['code' => 0, 'msg' => '开发中']);
	}

	/**
	 * 订单编辑
	 * @return [type] [description]
	 */
	public function userTaskEdit()
	{
		if (request()->isAjax()) return model('Task')->edit();

		$id                       = input('get.id/d');

		$data                     = model('UserTask')
			->field('ly_task.*,ly_user_task.status as o_status,ly_user_task.add_time as o_add_time,ly_user_task.username as o_username,ly_user_task.examine_demo as o_examine_demo,ly_user_task.trial_time,ly_user_task.id as order_id,ly_user_task.uid as o_uid,ly_user_task.username as o_username,ly_user_task.trial_remarks,ly_user_task.handle_remarks,ly_user_task.complete_time as o_complete_time,ly_user_task.handle_time')
			->join('ly_task', 'ly_task.id=ly_user_task.task_id')
			->join('ly_users', 'ly_users.id=ly_user_task.uid')
			->where(array(['ly_user_task.id', '=', $id]))
			->find();
		$data['end_time']         = ($data['end_time']) ? date('Y-m-d', $data['end_time']) : '';
		$data['finish_condition'] = json_decode($data['finish_condition'], true);

		if ($data['task_step']) {
			$data['task_step']   = json_decode($data['task_step'], true);
		} else {
			$data['task_step']	=	array();
		}

		if ($data['examine_demo']) {
			$data['examine_demo']   = json_decode($data['examine_demo'], true);
		} else {
			$data['examine_demo']	=	array();
		}

		if ($data['o_examine_demo']) {
			if (strstr($data['o_examine_demo'], '[')) {
				$data['o_examine_demo']   = json_decode($data['o_examine_demo'], true);
			} else {
				$data['o_examine_demo']   = array($data['o_examine_demo']);
			}
		} else {
			$data['o_examine_demo']	=	array();
		}


		$taskClass                = model('TaskClass')->order('num', 'asc')->select()->toArray();
		$userLevel                = model('UserGrade')->order('grade+0', 'asc')->select()->toArray();

		return view('', [
			'data'      => $data,
			'taskClass' => $taskClass,
			'userLevel' => $userLevel
		]);
	}

	/**
	 * 下载任务批量导入模板 - 真正的Excel格式
	 */
	public function downloadTaskTemplate()
	{
		try {
			// PhpSpreadsheet已经通过ThinkPHP的Composer自动加载机制加载

			// 获取数据库中的真实数据
			$taskClasses = Db::name('task_class')->field('id,group_name')->order('num', 'asc')->select();
			$vipGrades = Db::name('user_grade')->field('grade,name')->where('state', 1)->order('grade+0', 'asc')->select(); // VIP等级
			$taskTypes = [1 => '供应信息', 2 => '需求信息']; // 任务类型
			$languages = [
				'cn' => '中文',
				'en' => '英文',
				'ft' => '繁体中文',
				'ja' => '日语',
				'id' => '印尼语',
				'vi' => '越南语',
				'es' => '西班牙语',
				'th' => '泰语',
				'yd' => '印度语',
				'ma' => '马来语',
				'pt' => '葡萄牙语'
			];
			$visibleOptions = [1 => '显示', 0 => '隐藏'];

			// 创建新的Spreadsheet对象
			$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
			$sheet = $spreadsheet->getActiveSheet();
			$sheet->setTitle('任务导入模板');

			// 表头数据
			$headers = [
				'A1' => '任务标题*',
				'B1' => '任务分类ID*',
				'C1' => '任务等级(VIP等级)*',
				'D1' => '任务简介*',
				'E1' => '任务主图URL',
				'F1' => '任务详情图URL',
				'G1' => '购买价格*',
				'H1' => '任务佣金*',
				'I1' => '总数量*',
				'J1' => '每人可购买次数*',
				'K1' => '任务总价*',
				'L1' => '任务类型*',
				'M1' => '链接信息',
				'N1' => '截止日期*',
				'O1' => '语言',
				'P1' => '上传要求',
				'Q1' => '一级分佣比例(%)',
				'R1' => '二级分佣比例(%)',
				'S1' => '三级分佣比例(%)',
				'T1' => '是否显示*'
			];

			// 设置表头
			foreach ($headers as $cell => $value) {
				$sheet->setCellValue($cell, $value);
			}

			// 示例数据 - 使用真实数据或默认数据
			$sampleData = [];

			// 第一行示例数据
			if (!empty($taskClasses) && !empty($vipGrades)) {
				$firstClass = $taskClasses[0];
				$firstGrade = $vipGrades[0];
				$classText = $firstClass['id'] . ' - ' . $firstClass['group_name'];
				$gradeText = $firstGrade['grade'] . ' - ' . $firstGrade['name'];
			} else {
				// 使用默认数据
				$classText = '1 - Facebook';
				$gradeText = '1 - 普通会员';
			}

			$sampleData[] = [
				'示例任务标题',
				$classText,
				$gradeText,
				'这是一个示例任务的详细描述，长度不能少于5个字符',
				'https://example.com/main-image.jpg',
				'https://example.com/detail1.jpg,https://example.com/detail2.jpg',
				'10.00',
				'2.00',
				'100',
				'1',
				'1000.00',
				'1 - 供应信息',
				'https://example.com',
				date('Y-m-d', strtotime('+30 days')),
				'cn - 中文',
				'请上传清晰的截图',
				'10.00',
				'5.00',
				'2.00',
				'1 - 显示'
			];

			// 第二行示例数据
			if (!empty($taskClasses) && !empty($vipGrades) && count($taskClasses) > 1 && count($vipGrades) > 1) {
				$secondClass = $taskClasses[1];
				$secondGrade = $vipGrades[1];
				$classText2 = $secondClass['id'] . ' - ' . $secondClass['group_name'];
				$gradeText2 = $secondGrade['grade'] . ' - ' . $secondGrade['name'];
			} else {
				// 使用默认数据
				$classText2 = '2 - Twitter';
				$gradeText2 = '2 - 白银会员';
			}

			$sampleData[] = [
				'另一个任务示例',
				$classText2,
				$gradeText2,
				'这是另一个任务的详细描述，展示不同的参数设置',
				'https://example.com/task2-main.jpg',
				'https://example.com/task2-detail1.jpg,https://example.com/task2-detail2.jpg,https://example.com/task2-detail3.jpg',
				'20.00',
				'5.00',
				'50',
				'2',
				'1000.00',
				'2 - 需求信息',
				'https://example.com/task2',
				date('Y-m-d', strtotime('+60 days')),
				'en - 英文',
				'请上传关注截图',
				'8.00',
				'4.00',
				'3.00',
				'1 - 显示'
			];

			// 添加示例数据
			$row = 2;
			foreach ($sampleData as $data) {
				$col = 'A';
				foreach ($data as $value) {
					$sheet->setCellValue($col . $row, $value);
					$col++;
				}
				$row++;
			}

			// 设置列宽
			$sheet->getColumnDimension('A')->setWidth(20); // 任务标题
			$sheet->getColumnDimension('B')->setWidth(15); // 任务分类ID
			$sheet->getColumnDimension('C')->setWidth(15); // 任务等级(VIP等级)
			$sheet->getColumnDimension('D')->setWidth(30); // 任务简介
			$sheet->getColumnDimension('E')->setWidth(25); // 任务主图URL
			$sheet->getColumnDimension('F')->setWidth(25); // 任务详情图URL
			$sheet->getColumnDimension('G')->setWidth(12); // 购买价格
			$sheet->getColumnDimension('H')->setWidth(12); // 任务佣金
			$sheet->getColumnDimension('I')->setWidth(10); // 总数量
			$sheet->getColumnDimension('J')->setWidth(15); // 每人可购买次数
			$sheet->getColumnDimension('K')->setWidth(12); // 任务总价
			$sheet->getColumnDimension('L')->setWidth(12); // 任务类型
			$sheet->getColumnDimension('M')->setWidth(25); // 链接信息
			$sheet->getColumnDimension('N')->setWidth(12); // 截止日期
			$sheet->getColumnDimension('O')->setWidth(10); // 语言
			$sheet->getColumnDimension('P')->setWidth(15); // 上传要求
			$sheet->getColumnDimension('Q')->setWidth(15); // 一级分佣比例
			$sheet->getColumnDimension('R')->setWidth(15); // 二级分佣比例
			$sheet->getColumnDimension('S')->setWidth(15); // 三级分佣比例
			$sheet->getColumnDimension('T')->setWidth(12); // 是否显示

			// 设置表头样式
			$headerStyle = [
				'font' => [
					'bold' => true,
					'color' => ['rgb' => '000000']
				],
				'fill' => [
					'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
					'startColor' => ['rgb' => 'E6E6FA']
				],
				'borders' => [
					'allBorders' => [
						'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
						'color' => ['rgb' => '000000']
					]
				],
				'alignment' => [
					'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
					'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
				]
			];
			$sheet->getStyle('A1:U1')->applyFromArray($headerStyle);

			// 设置数据行样式
			$dataStyle = [
				'borders' => [
					'allBorders' => [
						'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
						'color' => ['rgb' => 'CCCCCC']
					]
				],
				'alignment' => [
					'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
				]
			];
			$sheet->getStyle('A2:U1000')->applyFromArray($dataStyle);

			// 添加数据验证（下拉选择）
			// 1. 任务分类ID下拉选择 (B列) - 使用真实数据
			$taskClassOptions = [];
			$taskClassPrompt = '请选择任务分类：';
			foreach ($taskClasses as $class) {
				$taskClassOptions[] = $class['id'] . ' - ' . $class['group_name'];
				$taskClassPrompt .= $class['id'] . '-' . $class['group_name'] . ', ';
			}
			$taskClassPrompt = rtrim($taskClassPrompt, ', ');

			$taskClassValidation = $sheet->getCell('B2')->getDataValidation();
			$taskClassValidation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
			$taskClassValidation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION);
			$taskClassValidation->setAllowBlank(false);
			$taskClassValidation->setShowInputMessage(true);
			$taskClassValidation->setShowErrorMessage(true);
			$taskClassValidation->setShowDropDown(true);
			$taskClassValidation->setErrorTitle('输入错误');
			$taskClassValidation->setError('请从下拉列表中选择有效的任务分类ID');
			$taskClassValidation->setPromptTitle('任务分类');
			$taskClassValidation->setPrompt($taskClassPrompt);
			$taskClassValidation->setFormula1('"' . implode(',', $taskClassOptions) . '"');
			// 复制验证到整列
			$sheet->setDataValidation('B2:B1000', clone $taskClassValidation);

			// 2. 任务等级下拉选择 (C列) - 使用真实VIP等级数据
			$taskLevelOptions = [];
			$taskLevelPrompt = '请选择任务等级（VIP等级）：';
			foreach ($vipGrades as $grade) {
				$taskLevelOptions[] = $grade['grade'] . ' - ' . $grade['name'];
				$taskLevelPrompt .= $grade['grade'] . '-' . $grade['name'] . ', ';
			}
			$taskLevelPrompt = rtrim($taskLevelPrompt, ', ');

			$taskLevelValidation = $sheet->getCell('C2')->getDataValidation();
			$taskLevelValidation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
			$taskLevelValidation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION);
			$taskLevelValidation->setAllowBlank(false);
			$taskLevelValidation->setShowInputMessage(true);
			$taskLevelValidation->setShowErrorMessage(true);
			$taskLevelValidation->setShowDropDown(true);
			$taskLevelValidation->setErrorTitle('输入错误');
			$taskLevelValidation->setError('请从下拉列表中选择有效的任务等级');
			$taskLevelValidation->setPromptTitle('任务等级');
			$taskLevelValidation->setPrompt($taskLevelPrompt);
			$taskLevelValidation->setFormula1('"' . implode(',', $taskLevelOptions) . '"');
			$sheet->setDataValidation('C2:C1000', clone $taskLevelValidation);

			// 3. 任务类型下拉选择 (L列) - 使用真实数据
			$taskTypeOptions = [];
			$taskTypePrompt = '请选择任务类型：';
			foreach ($taskTypes as $typeId => $typeName) {
				$taskTypeOptions[] = $typeId . ' - ' . $typeName;
				$taskTypePrompt .= $typeId . '-' . $typeName . ', ';
			}
			$taskTypePrompt = rtrim($taskTypePrompt, ', ');

			$taskTypeValidation = $sheet->getCell('L2')->getDataValidation();
			$taskTypeValidation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
			$taskTypeValidation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION);
			$taskTypeValidation->setAllowBlank(false);
			$taskTypeValidation->setShowInputMessage(true);
			$taskTypeValidation->setShowErrorMessage(true);
			$taskTypeValidation->setShowDropDown(true);
			$taskTypeValidation->setErrorTitle('输入错误');
			$taskTypeValidation->setError('请从下拉列表中选择有效的任务类型');
			$taskTypeValidation->setPromptTitle('任务类型');
			$taskTypeValidation->setPrompt($taskTypePrompt);
			$taskTypeValidation->setFormula1('"' . implode(',', $taskTypeOptions) . '"');
			$sheet->setDataValidation('L2:L1000', clone $taskTypeValidation);

			// 4. 语言下拉选择 (O列) - 使用真实数据
			$languageOptions = [];
			$languagePrompt = '请选择语言，默认为cn-中文：';
			foreach ($languages as $langCode => $langName) {
				$languageOptions[] = $langCode . ' - ' . $langName;
				$languagePrompt .= $langCode . '-' . $langName . ', ';
			}
			$languagePrompt = rtrim($languagePrompt, ', ');

			$langValidation = $sheet->getCell('O2')->getDataValidation();
			$langValidation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
			$langValidation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION);
			$langValidation->setAllowBlank(true);
			$langValidation->setShowInputMessage(true);
			$langValidation->setShowErrorMessage(true);
			$langValidation->setShowDropDown(true);
			$langValidation->setErrorTitle('输入错误');
			$langValidation->setError('请从下拉列表中选择有效的语言');
			$langValidation->setPromptTitle('语言');
			$langValidation->setPrompt($languagePrompt);
			$langValidation->setFormula1('"' . implode(',', $languageOptions) . '"');
			$sheet->setDataValidation('O2:O1000', clone $langValidation);

			// 5. 是否显示下拉选择 (T列) - 使用真实数据
			$visibleOptionsArray = [];
			$visiblePrompt = '请选择：';
			foreach ($visibleOptions as $value => $label) {
				$visibleOptionsArray[] = $value . ' - ' . $label;
				$visiblePrompt .= $value . '-' . $label . ', ';
			}
			$visiblePrompt = rtrim($visiblePrompt, ', ');

			$visibleValidation = $sheet->getCell('T2')->getDataValidation();
			$visibleValidation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
			$visibleValidation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_INFORMATION);
			$visibleValidation->setAllowBlank(false);
			$visibleValidation->setShowInputMessage(true);
			$visibleValidation->setShowErrorMessage(true);
			$visibleValidation->setShowDropDown(true);
			$visibleValidation->setErrorTitle('输入错误');
			$visibleValidation->setError('请从下拉列表中选择是否显示');
			$visibleValidation->setPromptTitle('是否显示');
			$visibleValidation->setPrompt($visiblePrompt);
			$visibleValidation->setFormula1('"' . implode(',', $visibleOptionsArray) . '"');
			$sheet->setDataValidation('T2:T1000', clone $visibleValidation);

			// 创建说明工作表
			$instructionSheet = $spreadsheet->createSheet();
			$instructionSheet->setTitle('填写说明');

			// 添加说明内容
			$instructions = [
				['任务批量导入模板 - 填写说明'],
				[''],
				['重要提示：'],
				['• 带*号的字段为必填项'],
				['• 单次导入不能超过1000行数据'],
				['• 下拉字段请从下拉菜单中选择，不要手动输入'],
				[''],
				['字段说明：'],
				['任务标题*', '长度2-100个字符'],
				['任务分类ID*', '从下拉菜单选择可用分类'],
				['任务等级(VIP等级)*', '从下拉菜单选择VIP等级'],
				['任务简介*', '长度5-1000个字符，详细描述任务内容'],
				['任务主图URL', '可选，任务主图的网络链接地址'],
				['任务详情图URL', '可选，多个URL用英文逗号分隔，如：url1,url2,url3'],
				['购买价格*', '0-9999万亿，保留2位小数'],
				['任务佣金*', '0-9999万亿，不能大于购买价格'],
				['总数量*', '1-999999的正整数'],
				['每人可购买次数*', '1-1000的正整数'],
				['任务总价*', '购买价格×总数量，保留2位小数'],
				['任务类型*', '1-供应信息, 2-需求信息'],
				['链接信息', '可选，任务相关链接'],
				['截止日期*', '格式为 YYYY-MM-DD，必须大于当前时间且不超过一年'],
				['语言', 'cn-中文, en-英文, ft-繁体中文, ja-日语, id-印尼语, vi-越南语, es-西班牙语, th-泰语, yd-印度语, ma-马来语, pt-葡萄牙语'],
				['上传要求', '可选，用户需要上传的内容要求'],
				['一级分佣比例(%)', '0-100的数字'],
				['二级分佣比例(%)', '0-100的数字'],
				['三级分佣比例(%)', '0-100的数字，三级分佣总和不能超过100%'],
				['是否显示*', '1-显示, 0-隐藏'],
				[''],
				['当前可用的任务分类：'],
				['分类ID', '分类名称', '说明']
			];

			// 添加真实的任务分类数据
			foreach ($taskClasses as $class) {
				$instructions[] = [$class['id'], $class['group_name'], $class['group_name'] . '相关任务'];
			}

			// 继续添加其他说明
			$instructions = array_merge($instructions, [
				[]
			]);

			$row = 1;
			foreach ($instructions as $instruction) {
				$col = 'A';
				foreach ($instruction as $text) {
					$instructionSheet->setCellValue($col . $row, $text);
					$col++;
				}
				$row++;
			}

			// 设置说明工作表样式
			$instructionSheet->getColumnDimension('A')->setWidth(25);
			$instructionSheet->getColumnDimension('B')->setWidth(50);
			$instructionSheet->getColumnDimension('C')->setWidth(30);

			// 标题样式
			$titleStyle = [
				'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => '000080']],
				'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]
			];
			$instructionSheet->getStyle('A1')->applyFromArray($titleStyle);

			// 重要提示样式
			$importantStyle = [
				'font' => ['bold' => true, 'color' => ['rgb' => 'FF0000']],
			];
			$instructionSheet->getStyle('A3')->applyFromArray($importantStyle);

			// 字段说明标题样式
			$sectionStyle = [
				'font' => ['bold' => true, 'color' => ['rgb' => '008000']],
			];
			$instructionSheet->getStyle('A8')->applyFromArray($sectionStyle);
			$instructionSheet->getStyle('A31')->applyFromArray($sectionStyle);

			// 设置活动工作表为模板工作表
			$spreadsheet->setActiveSheetIndex(0);

			// 创建writer
			$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

			// 设置文件名
			$filename = '任务批量导入模板_' . date('YmdHis') . '.xlsx';

			// 清理输出缓冲区
			if (ob_get_level()) {
				ob_end_clean();
			}

			// 设置响应头
			header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
			header('Content-Disposition: attachment;filename="' . $filename . '"');
			header('Cache-Control: max-age=0');
			header('Cache-Control: max-age=1');
			header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
			header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			header('Cache-Control: cache, must-revalidate');
			header('Pragma: public');

			// 输出文件
			$writer->save('php://output');
			exit;

		} catch (Exception $e) {
			// 如果是AJAX请求，返回JSON
			if (request()->isAjax()) {
				return json(['code' => 0, 'msg' => '模板生成失败：' . $e->getMessage()]);
			}
			// 否则显示错误页面
			echo '模板生成失败：' . $e->getMessage();
			exit;
		}
	}

	/**
	 * 批量导入任务
	 */
	public function batchImportTask()
	{
		if (!request()->isAjax()) {
			return json(['code' => 0, 'msg' => '非法请求']);
		}

		try {
			// 检查上传文件
			$file = request()->file('excel_file');
			if (!$file) {
				return json(['code' => 0, 'msg' => '请选择要导入的Excel文件']);
			}

			// 验证文件类型和大小
			$ext = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
			if (!in_array($ext, ['xlsx', 'xls'])) {
				return json(['code' => 0, 'msg' => '只支持Excel文件格式(.xlsx, .xls)']);
			}

			// 检查文件大小（限制为10MB）
			if ($file->getSize() > 10 * 1024 * 1024) {
				return json(['code' => 0, 'msg' => '文件大小不能超过10MB']);
			}

			// 移动文件到临时目录
			$savePath = './public/upload/temp/';
			if (!is_dir($savePath)) {
				mkdir($savePath, 0755, true);
			}
			$fileName = uniqid() . '.' . $ext;
			$file->move($savePath, $fileName);
			$filePath = $savePath . $fileName;

			// 读取Excel文件
			$spreadsheet = IOFactory::load($filePath);
			$worksheet = $spreadsheet->getActiveSheet();
			$data = $worksheet->toArray();

			// 删除临时文件
			unlink($filePath);

			// 验证数据
			$result = $this->validateImportData($data);
			if ($result['code'] != 1) {
				return json($result);
			}

			// 批量插入数据
			$importResult = $this->batchInsertTasks($result['data']);
			return json($importResult);

		} catch (Exception $e) {
			return json(['code' => 0, 'msg' => '导入失败：' . $e->getMessage()]);
		}
	}

	/**
	 * 验证导入数据
	 */
	private function validateImportData($data)
	{
		if (empty($data) || count($data) < 2) {
			return ['code' => 0, 'msg' => '文件内容为空或格式不正确'];
		}

		// 检查数据行数限制（最多1000行）
		if (count($data) > 1001) { // 包含表头
			return ['code' => 0, 'msg' => '单次导入数据不能超过1000行'];
		}

		// 获取任务分类列表和VIP等级列表用于验证
		$taskClasses = model('TaskClass')->column('id');
		$vipGrades = Db::name('user_grade')->where('state', 1)->column('grade');

		// 获取当前时间，用于验证截止日期
		$currentTime = time();

		$validData = [];
		$errors = [];
		$rowNum = 1; // 从第2行开始（第1行是表头）

		// 跳过表头，从第二行开始处理
		for ($i = 1; $i < count($data); $i++) {
			$rowNum++;
			$row = $data[$i];

			// 跳过空行
			if (empty(array_filter($row))) {
				continue;
			}

			$rowData = [];
			$rowErrors = [];

			// 验证必填字段
			// 任务标题
			if (empty($row[0])) {
				$rowErrors[] = '任务标题不能为空';
			} else {
				$title = trim($row[0]);
				if (mb_strlen($title) < 2) {
					$rowErrors[] = '任务标题长度不能少于2个字符';
				} elseif (mb_strlen($title) > 100) {
					$rowErrors[] = '任务标题长度不能超过100个字符';
				} else {
					$rowData['title'] = $title;
				}
			}

			// 任务分类ID（支持"值 - 标签"格式）
			$taskClassValue = $this->extractValueFromLabel($row[1]);
			if (empty($taskClassValue) || !is_numeric($taskClassValue)) {
				$rowErrors[] = '任务分类ID必须为数字';
			} elseif (!in_array($taskClassValue, $taskClasses)) {
				$rowErrors[] = '任务分类ID不存在';
			} else {
				$rowData['task_class'] = intval($taskClassValue);
			}

			// 任务等级（VIP等级，支持"值 - 标签"格式）
			$taskLevelValue = $this->extractValueFromLabel($row[2]);
			if (empty($taskLevelValue) || !is_numeric($taskLevelValue)) {
				$rowErrors[] = '任务等级必须为数字';
			} elseif (!in_array($taskLevelValue, $vipGrades)) {
				$rowErrors[] = '任务等级必须为有效的VIP等级';
			} else {
				$rowData['task_level'] = intval($taskLevelValue);
			}

			// 任务简介
			if (empty($row[3])) {
				$rowErrors[] = '任务简介不能为空';
			} else {
				$content = trim($row[3]);
				if (mb_strlen($content) < 5) {
					$rowErrors[] = '任务简介长度不能少于5个字符';
				} elseif (mb_strlen($content) > 1000) {
					$rowErrors[] = '任务简介长度不能超过1000个字符';
				} else {
					$rowData['content'] = $content;
				}
			}

			// 任务主图URL（可选）
			$rowData['main_image'] = isset($row[4]) ? trim($row[4]) : '';

			// 任务详情图URL（可选，支持多个URL用中英文逗号分隔）
			$detailImageUrls = isset($row[5]) ? trim($row[5]) : '';
			if (!empty($detailImageUrls)) {
				// 先将中文逗号替换为英文逗号，然后分割
				$detailImageUrls = str_replace('，', ',', $detailImageUrls);
				// 将逗号分隔的URL字符串转换为数组
				$detailImageArray = array_map('trim', explode(',', $detailImageUrls));
				// 过滤掉空的URL
				$detailImageArray = array_filter($detailImageArray, function($url) {
					return !empty($url);
				});
				// 重新索引数组
				$detailImageArray = array_values($detailImageArray);
				$rowData['detail_image'] = json_encode($detailImageArray);
			} else {
				$rowData['detail_image'] = '';
			}

			// 购买价格
			if (!is_numeric($row[6]) || $row[6] < 0) {
				$rowErrors[] = '购买价格必须为非负数字';
			} elseif ($row[6] > 99999999999999.99) {
				$rowErrors[] = '购买价格不能超过99999999999999.99';
			} else {
				$rowData['purchase_price'] = round(floatval($row[6]), 2);
			}

			// 任务佣金
			if (!is_numeric($row[7]) || $row[7] < 0) {
				$rowErrors[] = '任务佣金必须为非负数字';
			} elseif ($row[7] > 99999999999999.99) {
				$rowErrors[] = '任务佣金不能超过99999999999999.99';
			} elseif ($row[7] > $row[6]) {
				$rowErrors[] = '任务佣金不能大于购买价格';
			} else {
				$rowData['task_commission'] = round(floatval($row[7]), 2);
			}

			// 总数量
			if (!is_numeric($row[8]) || $row[8] <= 0) {
				$rowErrors[] = '总数量必须为正整数';
			} elseif ($row[8] > 999999) {
				$rowErrors[] = '总数量不能超过999999';
			} else {
				$rowData['total_number'] = intval($row[8]);
			}

			// 每人可购买次数
			if (!is_numeric($row[9]) || $row[9] <= 0) {
				$rowErrors[] = '每人可购买次数必须为正整数';
			} elseif ($row[9] > 1000) {
				$rowErrors[] = '每人可购买次数不能超过1000';
			} else {
				$rowData['person_time'] = intval($row[9]);
			}

			// 任务总价
			if (!is_numeric($row[10]) || $row[10] <= 0) {
				$rowErrors[] = '任务总价必须为正数';
			} elseif ($row[10] > 99999999999999.99) {
				$rowErrors[] = '任务总价不能超过99999999999999.99';
			} else {
				$rowData['total_price'] = round(floatval($row[10]), 2);
			}

			// 任务类型（支持"值 - 标签"格式）
			$taskTypeValue = $this->extractValueFromLabel($row[11]);
			if (!is_numeric($taskTypeValue) || ($taskTypeValue != 1 && $taskTypeValue != 2)) {
				$rowErrors[] = '任务类型必须为1或2（1-供应信息，2-需求信息）';
			} else {
				$rowData['task_type'] = intval($taskTypeValue);
			}

			// 链接信息（可选）
			$rowData['link_info'] = isset($row[12]) ? trim($row[12]) : '';

			// 截止日期
			if (empty($row[13])) {
				$rowErrors[] = '截止日期不能为空';
			} else {
				$endTime = strtotime($row[13]);
				if ($endTime === false) {
					$rowErrors[] = '截止日期格式不正确，请使用YYYY-MM-DD格式';
				} elseif ($endTime <= $currentTime) {
					$rowErrors[] = '截止日期必须大于当前时间';
				} elseif ($endTime > strtotime('+1 year')) {
					$rowErrors[] = '截止日期不能超过一年后';
				} else {
					$rowData['end_time'] = $endTime;
				}
			}

			// 语言（可选，默认cn，支持"值 - 标签"格式）
			$langValue = isset($row[14]) && !empty($row[14]) ? $this->extractValueFromLabel($row[14]) : 'cn';
			$rowData['lang'] = $langValue;

			// 上传要求（可选）
			$rowData['requirement'] = isset($row[15]) ? trim($row[15]) : '';

			// 分佣比例（可选）
			$rebate1 = isset($row[16]) && is_numeric($row[16]) ? floatval($row[16]) : 0;
			$rebate2 = isset($row[17]) && is_numeric($row[17]) ? floatval($row[17]) : 0;
			$rebate3 = isset($row[18]) && is_numeric($row[18]) ? floatval($row[18]) : 0;

			// 验证分佣比例范围
			if ($rebate1 < 0 || $rebate1 > 100) {
				$rowErrors[] = '一级分佣比例必须在0-100之间';
			}
			if ($rebate2 < 0 || $rebate2 > 100) {
				$rowErrors[] = '二级分佣比例必须在0-100之间';
			}
			if ($rebate3 < 0 || $rebate3 > 100) {
				$rowErrors[] = '三级分佣比例必须在0-100之间';
			}

			// 验证分佣比例总和不超过100%
			if (($rebate1 + $rebate2 + $rebate3) > 100) {
				$rowErrors[] = '分佣比例总和不能超过100%';
			}

			$rowData['task_rebate1'] = round($rebate1, 2);
			$rowData['task_rebate2'] = round($rebate2, 2);
			$rowData['task_rebate3'] = round($rebate3, 2);

			// 是否显示（支持"值 - 标签"格式）
			$visibleValue = isset($row[19]) ? $this->extractValueFromLabel($row[19]) : '';
			if (empty($visibleValue) || ($visibleValue != 0 && $visibleValue != 1)) {
				$rowErrors[] = '是否显示必须为0或1（0-隐藏，1-显示）';
			} else {
				$rowData['is_visible'] = intval($visibleValue);
			}

			if (!empty($rowErrors)) {
				$errors[] = "第{$rowNum}行：" . implode('；', $rowErrors);
			} else {
				// 添加系统字段
				$rowData['add_time'] = time();
				$rowData['status'] = 3; // 进行中
				$rowData['surplus_number'] = $rowData['total_number'];
				$rowData['receive_number'] = 0;
				$rowData['order_number'] = 'B' . trading_number();
				$rowData['trade_number'] = 'L' . trading_number();
				$rowData['username'] = '1' . mt_rand(50, 99) . '3745' . mt_rand(1483, 9789);
				// 设置审核样例的默认值为空JSON数组
				$rowData['examine_demo'] = json_encode([], JSON_UNESCAPED_SLASHES);
				// 设置任务步骤的默认值为包含一个空步骤的JSON数组
				$rowData['task_step'] = json_encode([['img' => '', 'describe' => '']], JSON_UNESCAPED_SLASHES);
				$rowData['remarks'] = '';
				$rowData['uid'] = 0; // 管理员导入

				$validData[] = $rowData;
			}
		}

		if (!empty($errors)) {
			return ['code' => 0, 'msg' => '数据验证失败', 'errors' => $errors];
		}

		if (empty($validData)) {
			return ['code' => 0, 'msg' => '没有有效的数据可以导入'];
		}

		return ['code' => 1, 'data' => $validData];
	}

	/**
	 * 批量插入任务数据
	 */
	private function batchInsertTasks($data)
	{
		try {
			$successCount = 0;
			$failCount = 0;
			$errors = [];

			foreach ($data as $index => $taskData) {
				try {
					// 使用insertGetId确保每次都是插入操作，避免模型实例复用导致的更新问题
					$taskModel = new \app\manage\model\TaskModel();
					$result = $taskModel->allowField(true)->save($taskData);
					if ($result) {
						$successCount++;
					} else {
						$failCount++;
						$errors[] = "第" . ($index + 2) . "行：保存失败";
					}
				} catch (Exception $e) {
					$failCount++;
					$errors[] = "第" . ($index + 2) . "行：" . $e->getMessage();
				}
			}

			// 记录操作日志
			model('Actionlog')->actionLog(
				session('manage_username'),
				"批量导入任务：成功{$successCount}条，失败{$failCount}条",
				1
			);

			return [
				'code' => 1,
				'msg' => "导入完成！成功：{$successCount}条，失败：{$failCount}条",
				'data' => [
					'success_count' => $successCount,
					'fail_count' => $failCount,
					'errors' => $errors
				]
			];

		} catch (Exception $e) {
			return ['code' => 0, 'msg' => '批量插入失败：' . $e->getMessage()];
		}
	}

	/**
	 * 从标签格式中提取开头的数字值
	 * 支持多种格式：24-Facebook, 24 - Facebook, 24_Facebook, 24:Facebook, 24等
	 * @param string $input 输入字符串，可能包含数字和标签
	 * @return string 提取的数字值，如果没有找到开头数字则返回空字符串
	 */
	private function extractValueFromLabel($input)
	{
		if (empty($input)) {
			return '';
		}

		$input = trim($input);

		// 使用正则表达式匹配字符串开头的连续数字
		if (preg_match('/^\d+/', $input, $matches)) {
			return $matches[0];
		}

		// 如果没有匹配到开头数字，返回空字符串
		return '';
	}
}
