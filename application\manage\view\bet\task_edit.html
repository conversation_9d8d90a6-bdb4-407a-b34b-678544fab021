<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加任务</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务分类</label>
                                <div class="layui-input-inline">
                                    <select name="task_class" lay-verify="required" lay-search="">
                                        {foreach $taskClass as $key=>$value }
                                        <option value="{$value.id}"{if $data.task_class == $value.id} selected{/if}>{$value.lang}-{$value.group_name}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">语言</label>
                                <div class="layui-input-inline">
                                     <select name="lang" lay-verify="required" lay-search="">
                                     {foreach :config('custom.lang') as $key=>$value }
                                        <option value="{$key}" {if $data.lang eq $key} selected{/if}>{$value}</option>
									 {/foreach}

                                    </select>
                                </div>
                            </div>
							{if $tplid}
							<div class="layui-form-item">
                                <label class="layui-form-label">任务重复数量</label>
                                <div class="layui-input-block">
                                    <input type="text" name="repeat_num" autocomplete="off" value="{$data.repeat_num ?? ''}" placeholder="请输入任务重复数量" class="layui-input">
                                </div>
                            </div>
							{/if}
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务标题</label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" value="{$data.title ?? ''}" autocomplete="off" placeholder="请输入任务标题" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务简介</label>
                                <div class="layui-input-block">
                                    <textarea name="content" placeholder="请输入任务简介" class="layui-textarea">{$data.content ?? ''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务主图</label>
                                <div class="layui-input-block">
                                    <input type="text" name="main_image" id="main-image-url" value="{$data.main_image ?? ''}" autocomplete="off" placeholder="请输入主图URL链接" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">
                                        <img id="main-image-preview" src="{$data.main_image ?? ''}" style="max-width: 200px; max-height: 200px; margin-top: 10px; {if empty($data.main_image)}display: none;{/if}">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务详情图</label>
                                <div class="layui-input-block">
                                    <div id="detail-images-container">
                                        {php}
                                        $detail_images = [];
                                        if (!empty($data['detail_image'])) {
                                            if (is_array($data['detail_image'])) {
                                                // 已经是数组（从taskEdit方法传来）
                                                $detail_images = $data['detail_image'];
                                            } elseif (is_string($data['detail_image'])) {
                                                // 是字符串，尝试解码
                                                $decoded = json_decode($data['detail_image'], true);
                                                if (is_array($decoded)) {
                                                    $detail_images = $decoded;
                                                } else {
                                                    // 兼容旧数据（单个URL字符串）
                                                    $detail_images = [$data['detail_image']];
                                                }
                                            }
                                        }
                                        if (empty($detail_images)) {
                                            $detail_images = [''];
                                        }
                                        {/php}
                                        {foreach $detail_images as $key => $image_url}
                                        <div class="detail-image-item" style="margin-bottom: 10px;">
                                            <div style="display: flex; align-items: center;">
                                                <input type="text" name="detail_images[]" class="layui-input detail-image-url" value="{$image_url}" autocomplete="off" placeholder="请输入详情图URL链接" style="flex: 1; margin-right: 10px;">
                                                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger remove-detail-image" {if $key == 0 && count($detail_images) == 1}style="display: none;"{/if}>删除</button>
                                            </div>
                                            <div class="detail-image-preview-container" style="margin-top: 5px;">
                                                <img class="detail-image-preview" src="{$image_url}" style="max-width: 200px; max-height: 200px; {if empty($image_url)}display: none;{/if}">
                                            </div>
                                        </div>
                                        {/foreach}
                                    </div>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="add-detail-image">
                                        <i class="layui-icon layui-icon-addition"></i> 添加详情图
                                    </button>
                                    <div class="layui-form-mid layui-word-aux">支持添加多个详情图</div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">购买价格</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="purchase_price" value="{$data.purchase_price ?? 0}" autocomplete="off" placeholder="请输入购买价格" class="layui-input" step="0.01" min="0" max="99999999999999.99" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">用户购买任务需要支付的金额</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务佣金</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="task_commission" value="{$data.task_commission ?? 0}" autocomplete="off" placeholder="请输入任务佣金" class="layui-input" step="0.01" min="0" max="99999999999999.99" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">用户完成任务获得的佣金</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">购买数量</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="total_number" value="{$data.total_number ?? 0}" autocomplete="off" placeholder="请输入购买数量" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">购买次数</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="person_time" value="{$data.person_time ?? 0}" autocomplete="off" placeholder="请输入购买次数" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">次/人</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务总价</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="total_price" value="{$data.total_price ?? 0}" autocomplete="off" placeholder="请输入任务总价" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务类型</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="task_type" value="1" title="供应信息"{if $data.task_type == 1} checked{/if}>
                                    <input type="radio" name="task_type" value="2" title="需求信息"{if $data.task_type == 2} checked{/if}>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">链接信息</label>
                                <div class="layui-input-block">
                                    <input type="text" name="link_info" value="{$data.link_info ?? ''}" autocomplete="off" placeholder="请输入链接信息" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务级别</label>
                                <div class="layui-input-block">
                                    {foreach $userLevel as $key=>$value}
                                    <input type="radio" name="task_level" value="{$value.grade}" title="{$value.name}"{if $data.task_level eq $value.grade} checked{/if}>
                                    {/foreach}
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">截止日期</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="end_time" value="{$data.end_time}" autocomplete="off" placeholder="请选择截止日期" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">完成条件</label>
                                <div class="layui-input-block">
                                {if $data.finish_condition neq ''}
                                <input type="checkbox" name="finish_condition[0]" title="手机认证"{if in_array(0, $data.finish_condition)} checked{/if}>
                                <input type="checkbox" name="finish_condition[1]" title="微信认证"{if in_array(1, $data.finish_condition)} checked{/if}>
                                <input type="checkbox" name="finish_condition[2]" title="实名认证"{if in_array(2, $data.finish_condition)} checked{/if}>
                                <input type="checkbox" name="finish_condition[3]" title="身份认证"{if in_array(3, $data.finish_condition)} checked{/if}>
                                {/if}
                                {if $data.finish_condition eq ''}
                                <input type="checkbox" name="finish_condition[0]" title="手机认证"/>
                                <input type="checkbox" name="finish_condition[1]" title="微信认证"/>
                                <input type="checkbox" name="finish_condition[2]" title="实名认证"/>
                                <input type="checkbox" name="finish_condition[3]" title="身份认证"/>
                                {/if}
                              </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">上传要求</label>
                                <div class="layui-input-block">
                                    <textarea name="requirement" placeholder="请输入上传要求" class="layui-textarea">{$data.requirement}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">一级分佣比例</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="task_rebate1" value="{$data.task_rebate1 ?? '10.00'}" autocomplete="off" placeholder="请输入一级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">二级分佣比例</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="task_rebate2" value="{$data.task_rebate2 ?? '5.00'}" autocomplete="off" placeholder="请输入二级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">三级分佣比例</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="task_rebate3" value="{$data.task_rebate3 ?? '2.00'}" autocomplete="off" placeholder="请输入三级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">显示状态</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="is_visible" value="1" title="显示"{if $data.is_visible == 1} checked{/if}>
                                    <input type="radio" name="is_visible" value="0" title="隐藏"{if $data.is_visible == 0} checked{/if}>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">审核样例</label>
                                <div class="layui-upload layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal examine_demo">选择多文件</button>
                                    <div class="layui-form-mid layui-word-aux" style="float: right;">此项如不修改请不要上传文件，否则将覆盖原数据</div>
                                    <div class="layui-upload-list">
                                        <table class="layui-table">
                                        <thead>
                                            <tr>
                                                <th>文件名</th>
                                                <th>大小</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="demoList"></tbody>
                                        </table>
                                    </div>
                                    <button type="button" class="layui-btn" id="examine-demo-btn">开始上传</button>
                                </div>
                            </div>
                            <div class="layui-form-item step-holder">
                                <label class="layui-form-label">任务步骤</label>
                                {foreach $data.task_step as $key=>$value }
                                <div class="layui-input-block" style="border: 1px solid #d2d2d2;border-radius: 3px;padding: 12px;display: flex;margin-bottom: 10px;align-items: flex-start;">
                                    <h4 style="background-color: #FFB800;width: 40px;height: 40px;border-radius: 20px;line-height: 40px;font-size: 16px;margin-right: 10px;text-align: center;margin-top: 50px;">{$key}</h4>
                                    <div class="layui-upload-drag task-step" id="task-step-1" style="width: 10%;margin-right: 10px;">
                                        <i class="layui-icon layui-icon-upload"></i>
                                        <p>点击上传，或将文件拖拽到此处</p>
                                        <div {if !$value.img}class="layui-hide"{/if}>
                                            <hr>
                                            <img src="{$value.img ?? ''}" alt="上传成功后渲染" style="max-width: 150px">
                                            <p></p>
                                        </div>
                                    </div>
                                    <input type="hidden" name="task_step[{$key}][img]" value="{$value.img ?? ''}" class="layui-input">
                                    <textarea name="task_step[{$key}][describe]" placeholder="请输入步骤描述" class="layui-textarea" style="width: 70%;display: inline-block;height: 250px;">{$value.describe ?? ''}</textarea>
                                </div>
                                {/foreach}
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label"></label>
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-warm step-holder-add">
                                        <i class="layui-icon layui-icon-addition" style="font-size: 20px;"></i>
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-warm step-holder-del">
                                        <i class="layui-icon layui-icon-subtraction" style="font-size: 20px;"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
								{if !$tplid}
                                <input type="hidden" name="id" value="{$data.id ?? 0}" class="layui-input">
								{/if}
                                <button class="layui-btn" lay-submit lay-filter="project_sub" data-type="{$tplid ?'taskAdd':'taskEdit'}">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bet.js"></script>

</body>
</html>